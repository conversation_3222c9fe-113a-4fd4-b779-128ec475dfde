import { describe, test, expect, beforeEach } from '@jest/globals';
import { StepParser } from '../../backend/services/StepParser';
import { ActionType } from '../../shared/types';

describe('StepParser', () => {
  let stepParser: StepParser;

  beforeEach(() => {
    stepParser = new StepParser();
  });

  describe('parseStep', () => {
    test('should parse navigation step correctly', () => {
      const result = stepParser.parseStep('打开百度首页');
      
      expect(result.action).toBe(ActionType.NAVIGATE);
      expect(result.target).toContain('百度');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    test('should parse click step correctly', () => {
      const result = stepParser.parseStep('点击"登录按钮"');
      
      expect(result.action).toBe(ActionType.CLICK);
      expect(result.target).toBe('登录按钮');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    test('should parse type step correctly', () => {
      const result = stepParser.parseStep('在搜索框中输入"人工智能"');
      
      expect(result.action).toBe(ActionType.TYPE);
      expect(result.target).toBe('搜索框');
      expect(result.value).toBe('人工智能');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    test('should parse verify step correctly', () => {
      const result = stepParser.parseStep('验证页面标题包含"百度"');
      
      expect(result.action).toBe(ActionType.VERIFY);
      expect(result.target).toBe('页面标题');
      expect(result.value).toBe('百度');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    test('should handle unclear steps with lower confidence', () => {
      const result = stepParser.parseStep('做点什么');
      
      expect(result.action).toBe(ActionType.CUSTOM);
      expect(result.confidence).toBeLessThan(0.5);
      expect(result.suggestions).toContain('建议使用更明确的动作词，如：点击、输入、验证等');
    });
  });

  describe('validateStep', () => {
    test('should validate navigation step', () => {
      const parsedStep = stepParser.parseStep('打开https://www.baidu.com');
      const validation = stepParser.validateStep(parsedStep);
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should invalidate click step without target', () => {
      const parsedStep = {
        originalText: '点击',
        action: ActionType.CLICK,
        confidence: 0.8,
        suggestions: [],
      };
      
      const validation = stepParser.validateStep(parsedStep);
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('点击操作需要指定目标元素');
    });

    test('should invalidate type step without value', () => {
      const parsedStep = {
        originalText: '在输入框中输入',
        action: ActionType.TYPE,
        target: '输入框',
        confidence: 0.8,
        suggestions: [],
      };
      
      const validation = stepParser.validateStep(parsedStep);
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('输入操作需要指定目标元素和输入内容');
    });
  });

  describe('parseSteps', () => {
    test('should parse multiple steps correctly', () => {
      const steps = [
        '打开百度首页',
        '在搜索框中输入"测试"',
        '点击搜索按钮',
        '验证搜索结果包含"测试"'
      ];
      
      const results = stepParser.parseSteps(steps);
      
      expect(results).toHaveLength(4);
      expect(results[0].action).toBe(ActionType.NAVIGATE);
      expect(results[1].action).toBe(ActionType.TYPE);
      expect(results[2].action).toBe(ActionType.CLICK);
      expect(results[3].action).toBe(ActionType.VERIFY);
    });
  });
});
