import { describe, test, expect, beforeEach, jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { AutomationController } from '../../backend/controllers/AutomationController';
import automationRoutes from '../../backend/routes/automation';

// Mock dependencies
jest.mock('../../backend/services/AutomationEngine');
jest.mock('../../backend/services/DatabaseService');

describe('AutomationController', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/api/automation', automationRoutes);
  });

  describe('POST /api/automation/parse', () => {
    test('should parse steps successfully', async () => {
      const steps = [
        '打开百度首页',
        '在搜索框中输入"测试"',
        '点击搜索按钮'
      ];

      const response = await request(app)
        .post('/api/automation/parse')
        .send({ steps })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.parsedSteps).toHaveLength(3);
      expect(response.body.data.validationResults).toHaveLength(3);
    });

    test('should return validation error for empty steps', async () => {
      const response = await request(app)
        .post('/api/automation/parse')
        .send({ steps: [] })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });

    test('should return validation error for invalid request body', async () => {
      const response = await request(app)
        .post('/api/automation/parse')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });
  });

  describe('POST /api/automation/execute', () => {
    test('should start test execution successfully', async () => {
      const requestData = {
        steps: [
          '打开百度首页',
          '在搜索框中输入"测试"'
        ],
        config: {
          saveScreenshots: true,
          generateReport: true
        }
      };

      const response = await request(app)
        .post('/api/automation/execute')
        .send(requestData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('started');
      expect(response.body.data.executionId).toBeDefined();
    });

    test('should return validation error for empty steps', async () => {
      const response = await request(app)
        .post('/api/automation/execute')
        .send({ steps: [] })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });
  });

  describe('GET /api/automation/health', () => {
    test('should return health status', async () => {
      const response = await request(app)
        .get('/api/automation/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('healthy');
      expect(response.body.data.activeExecutions).toBeDefined();
      expect(response.body.data.uptime).toBeDefined();
    });
  });
});
