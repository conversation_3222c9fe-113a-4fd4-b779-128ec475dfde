[{"name": "百度搜索基础功能测试", "description": "测试百度搜索的基本功能，包括搜索和结果验证", "steps": ["打开百度首页", "在搜索框中输入'人工智能'", "点击搜索按钮", "验证搜索结果页面包含'人工智能'", "点击第一个搜索结果"], "tags": ["搜索", "百度", "基础功能"]}, {"name": "淘宝商品搜索测试", "description": "测试淘宝商品搜索和筛选功能", "steps": ["打开淘宝首页", "在搜索框中输入'iPhone 15'", "点击搜索按钮", "等待搜索结果加载完成", "点击价格筛选'1000-5000元'", "验证搜索结果中包含'iPhone 15'", "点击第一个商品"], "tags": ["电商", "淘宝", "商品搜索"]}, {"name": "GitHub登录测试", "description": "测试GitHub的登录功能", "steps": ["打开GitHub首页", "点击'Sign in'按钮", "在用户名输入框中输入测试用户名", "在密码输入框中输入测试密码", "点击'Sign in'按钮", "验证登录成功或显示错误信息"], "tags": ["登录", "GitHub", "认证"]}, {"name": "知乎文章浏览测试", "description": "测试知乎文章浏览和互动功能", "steps": ["打开知乎首页", "在搜索框中输入'前端开发'", "点击搜索按钮", "点击第一篇文章", "滚动到页面底部", "点击点赞按钮", "验证点赞状态变化"], "tags": ["社交", "知乎", "文章浏览"]}, {"name": "京东购物车测试", "description": "测试京东商品添加到购物车的功能", "steps": ["打开京东首页", "在搜索框中输入'笔记本电脑'", "点击搜索按钮", "点击第一个商品", "选择商品规格（如果有）", "点击'加入购物车'按钮", "点击'去购物车结算'", "验证商品已添加到购物车"], "tags": ["电商", "京东", "购物车"]}, {"name": "微博发布动态测试", "description": "测试微博发布动态的功能", "steps": ["打开微博首页", "点击登录按钮", "输入登录信息（需要预先配置）", "点击发布框", "输入测试内容'这是一条自动化测试动态'", "点击发布按钮", "验证动态发布成功"], "tags": ["社交", "微博", "发布内容"]}, {"name": "B站视频搜索测试", "description": "测试B站视频搜索和播放功能", "steps": ["打开B站首页", "在搜索框中输入'编程教程'", "点击搜索按钮", "点击视频分类", "点击第一个视频", "等待视频加载", "点击播放按钮", "验证视频开始播放"], "tags": ["视频", "B站", "搜索播放"]}, {"name": "网易云音乐搜索测试", "description": "测试网易云音乐的搜索和播放功能", "steps": ["打开网易云音乐首页", "在搜索框中输入'周杰伦'", "点击搜索按钮", "点击歌曲分类", "点击第一首歌曲", "点击播放按钮", "验证音乐开始播放"], "tags": ["音乐", "网易云", "搜索播放"]}, {"name": "豆瓣电影评分测试", "description": "测试豆瓣电影的搜索和评分功能", "steps": ["打开豆瓣电影首页", "在搜索框中输入'肖申克的救赎'", "点击搜索按钮", "点击第一个电影结果", "查看电影评分", "滚动到评论区", "验证页面包含电影信息和评分"], "tags": ["电影", "豆瓣", "评分"]}, {"name": "CSDN技术文章搜索测试", "description": "测试CSDN技术文章的搜索和阅读功能", "steps": ["打开CSDN首页", "在搜索框中输入'Vue.js教程'", "点击搜索按钮", "点击第一篇文章", "滚动阅读文章内容", "点击收藏按钮（如果有）", "验证文章内容加载完整"], "tags": ["技术", "CSDN", "文章阅读"]}]