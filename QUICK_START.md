# 🚀 AI浏览器自动化测试平台 - 快速开始

## ✅ 当前状态

项目已成功运行！后端服务器和前端界面都已启动。

## 🌐 访问地址

- **前端界面**: http://localhost:3000/simple.html
- **后端API**: http://localhost:3001
- **健康检查**: http://localhost:3001/health

## 🎯 核心功能

### 1. 自然语言测试步骤
支持中文自然语言描述的测试步骤，例如：
- "打开百度首页"
- "在搜索框中输入'人工智能'"
- "点击搜索按钮"
- "验证搜索结果包含'人工智能'"

### 2. AI智能优化
- 集成Gemini API（通过OpenRouter）
- 自动优化测试步骤
- 智能解析自然语言
- 提供改进建议

### 3. 实时执行监控
- 可视化执行进度
- 实时状态更新
- 步骤级别的成功/失败反馈
- 支持执行取消

## 🛠️ 使用方法

### 基本使用流程

1. **打开前端界面**
   ```
   http://localhost:3000/simple.html
   ```

2. **添加测试步骤**
   - 在左侧输入框中输入测试步骤
   - 点击"添加步骤"按钮
   - 重复添加多个步骤

3. **执行测试**
   - 点击"开始执行测试"按钮
   - 在右侧查看实时执行状态
   - 观察每个步骤的执行结果

4. **管理执行**
   - 可以随时点击"停止执行"取消测试
   - 查看详细的执行进度和结果

### 示例测试用例

#### 百度搜索测试
```
1. 打开百度首页
2. 在搜索框中输入"人工智能"
3. 点击搜索按钮
4. 验证搜索结果包含"人工智能"
```

#### 网站导航测试
```
1. 打开GitHub首页
2. 点击"Sign in"按钮
3. 验证登录页面加载完成
```

## 🔧 配置说明

### 环境变量配置

在 `.env` 文件中配置以下参数：

```env
# 服务器配置
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# AI配置（可选）
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 其他配置
HEADLESS=false
BROWSER_TIMEOUT=30000
SCREENSHOT_DIR=./screenshots
```

### AI功能配置

要启用完整的AI功能，需要：

1. 注册OpenRouter账号：https://openrouter.ai/
2. 获取API密钥
3. 在 `.env` 文件中设置 `OPENROUTER_API_KEY`

**注意**: 即使没有配置API密钥，基本的自动化功能仍然可以正常使用。

## 📡 API接口

### 主要API端点

1. **执行测试**
   ```bash
   POST /api/automation/execute
   Content-Type: application/json
   
   {
     "steps": ["打开百度首页", "在搜索框中输入测试"]
   }
   ```

2. **解析步骤**
   ```bash
   POST /api/automation/parse
   Content-Type: application/json
   
   {
     "steps": ["打开百度首页"]
   }
   ```

3. **查询执行状态**
   ```bash
   GET /api/automation/execution/{executionId}
   ```

4. **取消执行**
   ```bash
   POST /api/automation/execution/{executionId}/cancel
   ```

## 🔍 测试API

使用curl测试API：

```bash
# 健康检查
curl http://localhost:3001/health

# 解析测试步骤
curl -X POST http://localhost:3001/api/automation/parse \
  -H "Content-Type: application/json" \
  -d '{"steps":["打开百度首页","点击搜索按钮"]}'

# 执行测试
curl -X POST http://localhost:3001/api/automation/execute \
  -H "Content-Type: application/json" \
  -d '{"steps":["打开百度首页","在搜索框中输入测试"]}'
```

## 🚨 故障排除

### 常见问题

1. **前端无法访问**
   - 确认前端服务器在端口3000运行
   - 检查 http://localhost:3000/simple.html

2. **后端API错误**
   - 确认后端服务器在端口3001运行
   - 检查 http://localhost:3001/health

3. **CORS错误**
   - 确认 `.env` 文件中 `CORS_ORIGIN=http://localhost:3000`
   - 重启后端服务器

4. **AI功能不工作**
   - 检查 `OPENROUTER_API_KEY` 是否正确配置
   - 基本功能不依赖AI，仍可正常使用

### 重启服务

如果遇到问题，可以重启服务：

```bash
# 停止所有进程
pkill -f "ts-node"
pkill -f "python3 -m http.server"

# 重新启动后端
npx ts-node backend/simple-server.ts &

# 重新启动前端
cd frontend && python3 -m http.server 3000 &
```

## 🎉 成功运行

如果看到以下信息，说明系统运行正常：

1. **后端日志**：
   ```
   Server started on port 3001
   简化版AI自动化测试服务器已启动！
   ```

2. **前端访问**：
   - 能够打开 http://localhost:3000/simple.html
   - 界面显示正常，可以添加测试步骤

3. **API测试**：
   - http://localhost:3001/health 返回健康状态
   - 可以成功执行测试步骤

## 📞 技术支持

如果遇到问题：

1. 检查控制台日志输出
2. 确认所有依赖已正确安装
3. 验证端口3000和3001未被占用
4. 检查 `.env` 文件配置

---

**恭喜！您的AI浏览器自动化测试平台已成功运行！** 🎊
