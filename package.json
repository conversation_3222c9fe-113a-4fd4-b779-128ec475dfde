{"name": "ai-browser-automation-agent", "version": "1.0.0", "description": "基于浏览器的自动化测试AI智能体", "main": "dist/backend/server.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "ts-node backend/server.ts", "dev:frontend": "cd frontend && npm run dev", "build": "tsc", "start": "node dist/backend/simple-server.js", "test": "echo \"Tests will be added later\"", "simple": "ts-node backend/simple-server.ts", "setup-db": "node scripts/init-database.js", "test-db": "node scripts/test-database.js", "reset-db": "mysql -u root -p < scripts/setup-database.sql"}, "keywords": ["browser-automation", "ai-agent", "testing", "stagehand", "playwright"], "author": "AI Browser Automation Team", "license": "MIT", "dependencies": {"@browserbasehq/stagehand": "^2.4.1", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "mysql2": "^3.14.1", "playwright": "^1.53.2", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/jest": "^29.5.5", "@types/multer": "^1.4.8", "@types/node": "^20.8.0", "@types/uuid": "^9.0.5", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "concurrently": "^8.2.1", "eslint": "^8.49.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}