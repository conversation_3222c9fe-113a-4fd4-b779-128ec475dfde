{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.0", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "@tanstack/react-query": "^4.36.1", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}