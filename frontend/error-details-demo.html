<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试失败详情演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .error-details {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .error-summary {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        details {
            margin-top: 10px;
        }
        summary {
            cursor: pointer;
            font-weight: bold;
            padding: 5px 0;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            margin-top: 5px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .step-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 测试失败详情演示</h1>
        <p>这个页面演示如何查看测试执行失败的详细信息，包括失败原因、可能的解决方案等。</p>

        <div class="section">
            <h3>🎮 操作</h3>
            <button onclick="runFailingTest()">运行一个会失败的测试</button>
            <button onclick="showExistingError()">显示现有错误详情</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div id="results" style="display: none;">
            <h3>📊 执行结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let currentExecutionId = null;

        // 运行一个会失败的测试
        async function runFailingTest() {
            const button = document.querySelector('button');
            button.disabled = true;
            button.textContent = '执行中...';

            try {
                showResults('🚀 开始执行测试...', 'info');

                const response = await fetch('http://localhost:3001/api/automation/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        steps: [
                            "访问一个不存在的网站 http://nonexistent-website-12345.com",
                            "验证页面加载"
                        ]
                    })
                });

                const result = await response.json();

                if (result.success) {
                    currentExecutionId = result.data.executionId;
                    showResults(`✅ 测试启动成功！执行ID: ${currentExecutionId}`, 'success');
                    
                    // 等待测试完成
                    setTimeout(async () => {
                        await checkExecutionStatus(currentExecutionId);
                    }, 3000);
                } else {
                    showResults(`❌ 测试启动失败: ${result.error}`, 'error');
                }

            } catch (error) {
                showResults(`❌ 请求失败: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '运行一个会失败的测试';
            }
        }

        // 检查执行状态
        async function checkExecutionStatus(executionId) {
            try {
                const response = await fetch(`http://localhost:3001/api/automation/execution/${executionId}`);
                const result = await response.json();

                if (result.success) {
                    if (result.data.status === 'failed') {
                        showResults('❌ 测试执行失败，正在获取详细错误信息...', 'error');
                        await fetchErrorDetails(executionId);
                    } else if (result.data.status === 'completed') {
                        showResults('✅ 测试执行完成', 'success');
                    } else {
                        showResults(`📊 测试状态: ${result.data.status}`, 'info');
                    }
                }
            } catch (error) {
                showResults(`❌ 获取执行状态失败: ${error.message}`, 'error');
            }
        }

        // 获取详细错误信息
        async function fetchErrorDetails(executionId) {
            try {
                const response = await fetch(`http://localhost:3001/api/automation/execution/${executionId}/errors`);
                const result = await response.json();

                if (result.success && result.data.errorDetails.length > 0) {
                    displayErrorDetails(result.data);
                } else {
                    showResults('❌ 无法获取详细错误信息', 'error');
                }
            } catch (error) {
                showResults(`❌ 获取错误详情失败: ${error.message}`, 'error');
            }
        }

        // 显示现有错误详情（使用固定的执行ID）
        async function showExistingError() {
            const executionId = '27380ba6-bdd5-4efd-a311-34d8426262ff'; // 使用刚才创建的失败执行
            showResults('🔍 正在获取现有错误详情...', 'info');
            await fetchErrorDetails(executionId);
        }

        // 显示详细错误信息
        function displayErrorDetails(errorData) {
            let errorHtml = `
                <div class="error-summary">
                    <h4 style="color: #856404; margin-top: 0;">🚨 执行失败详情</h4>
                    <div class="step-info">
                        <strong>执行ID:</strong> ${errorData.executionId}<br>
                        <strong>总步骤数:</strong> ${errorData.totalSteps}<br>
                        <strong>失败步骤数:</strong> ${errorData.failedSteps}<br>
                        <strong>执行状态:</strong> ${errorData.executionStatus}
                    </div>
                </div>
            `;

            errorData.errorDetails.forEach((error, index) => {
                errorHtml += `
                    <div class="error-details">
                        <h5 style="color: #721c24; margin-top: 0;">❌ 步骤 ${error.stepIndex} 失败</h5>
                        <div class="step-info">
                            <strong>步骤描述:</strong> ${error.stepDescription}<br>
                            <strong>错误信息:</strong> ${error.errorMessage}<br>
                            <strong>执行时间:</strong> ${new Date(error.startTime).toLocaleString()}
                        </div>
                        
                        <details>
                            <summary>📋 执行日志</summary>
                            <pre>${error.logs.join('\n')}</pre>
                        </details>

                        <details>
                            <summary>🔍 可能原因</summary>
                            <ul>
                                ${error.possibleCauses.map(cause => `<li>${cause}</li>`).join('')}
                            </ul>
                        </details>

                        <details>
                            <summary>💡 解决建议</summary>
                            <ul>
                                ${error.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                            </ul>
                        </details>

                        ${error.screenshot ? `
                            <details>
                                <summary>📸 失败时截图</summary>
                                <img src="${error.screenshot}" alt="失败截图" style="max-width: 100%; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            </details>
                        ` : ''}
                    </div>
                `;
            });

            document.getElementById('resultContent').innerHTML = errorHtml;
            document.getElementById('results').style.display = 'block';
        }

        // 显示结果
        function showResults(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('resultContent');
            
            resultsDiv.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error-details' : 
                             type === 'success' ? 'success' : 'error-summary';
            
            contentDiv.innerHTML += `<div class="${className}"><strong>[${timestamp}]</strong> ${message}</div>`;
            
            // 滚动到底部
            contentDiv.scrollTop = contentDiv.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';
            currentExecutionId = null;
        }
    </script>
</body>
</html>
