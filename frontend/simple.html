<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI浏览器自动化测试平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #001529;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.8;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card h2 {
            margin-bottom: 20px;
            color: #001529;
            font-size: 18px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .steps-list {
            margin-top: 20px;
        }

        .step-item {
            background: #f9f9f9;
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .step-item .step-text {
            flex: 1;
        }

        .step-item .step-number {
            background: #1890ff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 12px;
        }

        .step-item .remove-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .execution-status {
            margin-top: 20px;
            padding: 16px;
            border-radius: 6px;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }

        .execution-status.running {
            background: #e6f7ff;
            border-color: #91d5ff;
        }

        .execution-status.failed {
            background: #fff2f0;
            border-color: #ffccc7;
        }

        .status-title {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #52c41a;
            transition: width 0.3s;
        }

        .step-results {
            margin-top: 16px;
        }

        .step-result {
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 4px;
            font-size: 13px;
        }

        .step-result.passed {
            background: #f6ffed;
            color: #52c41a;
        }

        .step-result.failed {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .step-result.running {
            background: #e6f7ff;
            color: #1890ff;
        }

        .empty-state {
            text-align: center;
            color: #999;
            padding: 40px 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI浏览器自动化测试平台</h1>
            <p>输入自然语言描述的测试步骤，AI将自动执行浏览器操作</p>
        </div>

        <div class="main-content">
            <div class="card">
                <h2>测试步骤配置</h2>

                <div class="form-group">
                    <label for="stepInput">输入测试步骤：</label>
                    <textarea
                        id="stepInput"
                        placeholder="例如：打开百度首页"
                        rows="3"
                    ></textarea>
                </div>

                <button class="btn" onclick="addStep()">添加步骤</button>

                <div class="steps-list" id="stepsList">
                    <div class="empty-state" id="emptyState">
                        暂无测试步骤，请添加步骤
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn" onclick="executeTest()" id="executeBtn" disabled>
                        开始执行测试
                    </button>
                    <button class="btn btn-danger" onclick="cancelTest()" id="cancelBtn" style="display: none; margin-left: 10px;">
                        停止执行
                    </button>
                </div>
            </div>

            <div class="card">
                <h2>执行状态</h2>

                <div id="executionStatus" style="display: none;">
                    <div class="execution-status" id="statusCard">
                        <div class="status-title" id="statusTitle">准备执行</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                        <div id="progressText">0 / 0 步骤完成</div>

                        <div class="step-results" id="stepResults"></div>
                    </div>
                </div>

                <div id="noExecution" class="empty-state">
                    暂无执行记录
                </div>
            </div>
        </div>
    </div>

    <script>
        let steps = [];
        let currentExecutionId = null;
        let statusInterval = null;

        function addStep() {
            const input = document.getElementById('stepInput');
            const stepText = input.value.trim();

            if (!stepText) {
                alert('请输入测试步骤');
                return;
            }

            steps.push(stepText);
            input.value = '';
            updateStepsList();
            updateExecuteButton();
        }

        function removeStep(index) {
            steps.splice(index, 1);
            updateStepsList();
            updateExecuteButton();
        }

        function updateStepsList() {
            const stepsList = document.getElementById('stepsList');

            if (steps.length === 0) {
                // 显示空状态
                stepsList.innerHTML = '<div class="empty-state" id="emptyState">暂无测试步骤，请添加步骤</div>';
            } else {
                // 显示步骤列表
                stepsList.innerHTML = steps.map((step, index) => `
                    <div class="step-item">
                        <div class="step-number">${index + 1}</div>
                        <div class="step-text">${step}</div>
                        <button class="remove-btn" onclick="removeStep(${index})">×</button>
                    </div>
                `).join('');
            }
        }

        function updateExecuteButton() {
            const executeBtn = document.getElementById('executeBtn');
            executeBtn.disabled = steps.length === 0 || currentExecutionId !== null;
        }

        async function executeTest() {
            if (steps.length === 0) {
                alert('请至少添加一个测试步骤');
                return;
            }

            try {
                const response = await fetch('http://localhost:3001/api/automation/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ steps })
                });

                const result = await response.json();

                if (result.success) {
                    currentExecutionId = result.data.executionId;
                    showExecutionStatus();
                    startStatusPolling();
                    updateButtons();
                } else {
                    alert('执行失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        async function cancelTest() {
            if (!currentExecutionId) return;

            try {
                const response = await fetch(`http://localhost:3001/api/automation/execution/${currentExecutionId}/cancel`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    stopStatusPolling();
                    currentExecutionId = null;
                    updateButtons();
                    alert('测试已取消');
                } else {
                    alert('取消失败: ' + result.error);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        function showExecutionStatus() {
            const executionStatus = document.getElementById('executionStatus');
            const noExecution = document.getElementById('noExecution');

            if (executionStatus) {
                executionStatus.style.display = 'block';
            }
            if (noExecution) {
                noExecution.style.display = 'none';
            }
        }

        function updateButtons() {
            const executeBtn = document.getElementById('executeBtn');
            const cancelBtn = document.getElementById('cancelBtn');

            if (currentExecutionId) {
                executeBtn.style.display = 'none';
                cancelBtn.style.display = 'inline-block';
            } else {
                executeBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'none';
                updateExecuteButton();
            }
        }

        function startStatusPolling() {
            statusInterval = setInterval(async () => {
                if (!currentExecutionId) return;

                try {
                    const response = await fetch(`http://localhost:3001/api/automation/execution/${currentExecutionId}`);
                    const result = await response.json();

                    if (result.success) {
                        updateExecutionDisplay(result.data);

                        if (result.data.status === 'completed' || result.data.status === 'failed') {
                            stopStatusPolling();
                            currentExecutionId = null;
                            updateButtons();
                        }
                    }
                } catch (error) {
                    console.error('状态查询失败:', error);
                }
            }, 2000);
        }

        function stopStatusPolling() {
            if (statusInterval) {
                clearInterval(statusInterval);
                statusInterval = null;
            }
        }

        function updateExecutionDisplay(execution) {
            const statusCard = document.getElementById('statusCard');
            const statusTitle = document.getElementById('statusTitle');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const stepResults = document.getElementById('stepResults');

            // 更新状态卡片样式
            statusCard.className = 'execution-status ' + execution.status;

            // 更新状态标题
            const statusMap = {
                'running': '正在执行',
                'completed': '执行完成',
                'failed': '执行失败',
                'pending': '准备执行'
            };
            statusTitle.textContent = statusMap[execution.status] || execution.status;

            // 更新进度
            const totalSteps = execution.results.length;
            const completedSteps = execution.results.filter(r => r.status === 'passed' || r.status === 'failed').length;
            const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

            progressFill.style.width = progress + '%';
            progressText.textContent = `${completedSteps} / ${totalSteps} 步骤完成`;

            // 更新步骤结果
            stepResults.innerHTML = execution.results.map((result, index) => `
                <div class="step-result ${result.status}">
                    步骤 ${index + 1}: ${result.status === 'passed' ? '✓ 成功' : result.status === 'failed' ? '✗ 失败' : '⏳ 执行中'}
                    ${result.error ? ` - ${result.error}` : ''}
                </div>
            `).join('');
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 支持回车键添加步骤
            document.getElementById('stepInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    addStep();
                }
            });
        });
    </script>
</body>
</html>
