<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL验证和编排功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .example-config {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 URL验证和编排功能演示</h1>
        <p>这个页面演示如何使用新的URL请求验证和编排功能。</p>

        <!-- 基本测试步骤 -->
        <div class="section">
            <h3>📝 测试步骤</h3>
            <div class="form-group">
                <label for="testSteps">测试步骤（每行一个）:</label>
                <textarea id="testSteps" placeholder="例如：&#10;访问 https://api.github.com/users/octocat&#10;验证响应状态码为200&#10;检查返回的用户信息">访问 https://jsonplaceholder.typicode.com/posts
验证响应包含100条记录
处理返回的数据</textarea>
            </div>
        </div>

        <!-- URL验证配置 -->
        <div class="section">
            <h3>✅ URL验证配置</h3>
            <div class="form-group">
                <label for="validationUrl">验证的URL:</label>
                <input type="text" id="validationUrl" value="https://jsonplaceholder.typicode.com/posts" placeholder="要验证的URL">
            </div>
            <div class="form-group">
                <label for="expectedStatus">期望状态码:</label>
                <input type="text" id="expectedStatus" value="200" placeholder="例如: 200 或 200,201">
            </div>
            <div class="form-group">
                <label for="maxResponseTime">最大响应时间(ms):</label>
                <input type="number" id="maxResponseTime" value="3000" placeholder="3000">
            </div>
            <div class="form-group">
                <label for="expectedCount">期望结果数量:</label>
                <input type="number" id="expectedCount" value="100" placeholder="期望的结果数量">
            </div>
            <div class="form-group">
                <label for="contentValidation">内容验证:</label>
                <input type="text" id="contentValidation" placeholder="例如: success 或 data.status" value="id">
            </div>
        </div>

        <!-- 请求编排配置 -->
        <div class="section">
            <h3>🔄 请求编排配置</h3>
            <div class="form-group">
                <label for="orchestrationId">编排规则ID:</label>
                <input type="text" id="orchestrationId" value="url1-success-trigger-url2" placeholder="规则唯一标识">
            </div>
            <div class="form-group">
                <label for="triggerType">触发类型:</label>
                <select id="triggerType">
                    <option value="url_success">URL成功时触发</option>
                    <option value="url_failure">URL失败时触发</option>
                    <option value="condition">条件触发</option>
                </select>
            </div>
            <div class="form-group">
                <label for="targetUrl">目标URL:</label>
                <input type="text" id="targetUrl" value="https://jsonplaceholder.typicode.com/posts/1" placeholder="触发后要请求的URL">
            </div>
            <div class="form-group">
                <label for="triggerDelay">触发延迟(ms):</label>
                <input type="number" id="triggerDelay" value="1000" placeholder="延迟时间">
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="section">
            <h3>🎮 操作</h3>
            <button onclick="executeWithValidation()" id="executeBtn">执行带验证的测试</button>
            <button onclick="showConfigExample()">显示配置示例</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <!-- 配置示例 -->
        <div id="configExample" class="example-config" style="display: none;">
            <h4>📋 配置示例代码</h4>
            <div class="code-block" id="exampleCode"></div>
        </div>

        <!-- 结果显示 -->
        <div id="results" class="results" style="display: none;">
            <h4>📊 执行结果</h4>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let currentExecutionId = null;
        let statusInterval = null;

        // 执行带验证的测试
        async function executeWithValidation() {
            const executeBtn = document.getElementById('executeBtn');
            executeBtn.disabled = true;
            executeBtn.textContent = '执行中...';

            try {
                // 收集配置
                const config = collectConfiguration();

                // 显示配置
                showResults('开始执行测试...', 'info');
                showResults(`配置信息: ${JSON.stringify(config, null, 2)}`, 'info');

                // 发送请求
                const response = await fetch('http://localhost:3001/api/automation/execute-with-validation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                });

                const result = await response.json();

                if (result.success) {
                    currentExecutionId = result.data.executionId;
                    showResults(`✅ 测试启动成功！执行ID: ${currentExecutionId}`, 'success');

                    // 开始轮询状态
                    startStatusPolling();
                } else {
                    showResults(`❌ 测试启动失败: ${result.error}`, 'error');
                }

            } catch (error) {
                showResults(`❌ 请求失败: ${error.message}`, 'error');
            } finally {
                executeBtn.disabled = false;
                executeBtn.textContent = '执行带验证的测试';
            }
        }

        // 收集配置信息
        function collectConfiguration() {
            const steps = document.getElementById('testSteps').value.split('\n').filter(s => s.trim());

            // URL验证配置
            const requestValidation = {
                url: document.getElementById('validationUrl').value,
                expectedStatusCode: document.getElementById('expectedStatus').value.split(',').map(s => parseInt(s.trim())),
                maxResponseTime: parseInt(document.getElementById('maxResponseTime').value),
                expectedResultCount: {
                    exact: parseInt(document.getElementById('expectedCount').value)
                },
                responseValidation: {
                    contains: document.getElementById('contentValidation').value
                }
            };

            // 请求编排配置
            const requestOrchestration = {
                id: document.getElementById('orchestrationId').value,
                name: '自动触发规则',
                trigger: {
                    type: document.getElementById('triggerType').value,
                    sourceUrl: document.getElementById('validationUrl').value
                },
                targetUrl: document.getElementById('targetUrl').value,
                execution: {
                    delay: parseInt(document.getElementById('triggerDelay').value)
                }
            };

            return {
                steps,
                requestValidation,
                requestOrchestration
            };
        }

        // 开始状态轮询
        function startStatusPolling() {
            // 先停止之前的轮询
            stopStatusPolling();

            let pollCount = 0;
            const maxPolls = 30; // 最多轮询30次（60秒）

            statusInterval = setInterval(async () => {
                if (!currentExecutionId) {
                    stopStatusPolling();
                    return;
                }

                pollCount++;
                if (pollCount > maxPolls) {
                    showResults('⏰ 轮询超时，停止状态检查', 'warning');
                    stopStatusPolling();
                    return;
                }

                try {
                    const response = await fetch(`http://localhost:3001/api/automation/execution/${currentExecutionId}`);

                    if (!response.ok) {
                        if (response.status === 404) {
                            showResults('❌ 执行记录未找到', 'error');
                            stopStatusPolling();
                            return;
                        }
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        updateExecutionStatus(result.data);

                        if (result.data.status === 'completed' || result.data.status === 'failed') {
                            showResults('✅ 执行完成，停止轮询', 'success');
                            stopStatusPolling();
                        }
                    } else {
                        showResults(`❌ 获取状态失败: ${result.error}`, 'error');
                        stopStatusPolling();
                    }
                } catch (error) {
                    console.error('状态查询失败:', error);
                    showResults(`❌ 网络错误: ${error.message}`, 'error');
                    // 网络错误时不立即停止，给几次重试机会
                    if (pollCount > 5) {
                        stopStatusPolling();
                    }
                }
            }, 2000);
        }

        // 停止状态轮询
        function stopStatusPolling() {
            if (statusInterval) {
                clearInterval(statusInterval);
                statusInterval = null;
            }
        }

        // 更新执行状态
        function updateExecutionStatus(data) {
            let statusText = `📊 执行状态: ${data.status}\n`;

            // 如果执行失败，获取详细错误信息
            if (data.status === 'failed') {
                fetchErrorDetails(data.executionId || currentExecutionId);
                return;
            }

            if (data.validationResults) {
                statusText += `\n✅ 验证结果:\n${JSON.stringify(data.validationResults, null, 2)}`;
            }

            if (data.orchestrationResults) {
                statusText += `\n🔄 编排结果:\n${JSON.stringify(data.orchestrationResults, null, 2)}`;
            }

            showResults(statusText, data.status === 'completed' ? 'success' : 'info');
        }

        // 获取详细错误信息
        async function fetchErrorDetails(executionId) {
            try {
                const response = await fetch(`http://localhost:3001/api/automation/execution/${executionId}/errors`);
                const result = await response.json();

                if (result.success && result.data.errorDetails.length > 0) {
                    displayErrorDetails(result.data);
                } else {
                    showResults('❌ 执行失败，但无法获取详细错误信息', 'error');
                }
            } catch (error) {
                console.error('获取错误详情失败:', error);
                showResults('❌ 执行失败，获取错误详情时发生网络错误', 'error');
            }
        }

        // 显示详细错误信息
        function displayErrorDetails(errorData) {
            let errorHtml = `
                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 10px 0;">
                    <h4 style="color: #856404; margin-top: 0;">🚨 执行失败详情</h4>
                    <p><strong>执行ID:</strong> ${errorData.executionId}</p>
                    <p><strong>总步骤数:</strong> ${errorData.totalSteps}</p>
                    <p><strong>失败步骤数:</strong> ${errorData.failedSteps}</p>
                </div>
            `;

            errorData.errorDetails.forEach((error, index) => {
                errorHtml += `
                    <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 10px 0;">
                        <h5 style="color: #721c24; margin-top: 0;">❌ 步骤 ${error.stepIndex} 失败</h5>
                        <p><strong>步骤描述:</strong> ${error.stepDescription}</p>
                        <p><strong>错误信息:</strong> ${error.errorMessage}</p>
                        <p><strong>执行时间:</strong> ${new Date(error.startTime).toLocaleTimeString()}</p>

                        <details style="margin-top: 10px;">
                            <summary style="cursor: pointer; color: #721c24; font-weight: bold;">📋 执行日志</summary>
                            <pre style="background-color: #f1f1f1; padding: 10px; border-radius: 4px; margin-top: 5px; white-space: pre-wrap;">${error.logs.join('\n')}</pre>
                        </details>

                        <details style="margin-top: 10px;">
                            <summary style="cursor: pointer; color: #721c24; font-weight: bold;">🔍 可能原因</summary>
                            <ul style="margin-top: 5px;">
                                ${error.possibleCauses.map(cause => `<li>${cause}</li>`).join('')}
                            </ul>
                        </details>

                        <details style="margin-top: 10px;">
                            <summary style="cursor: pointer; color: #721c24; font-weight: bold;">💡 解决建议</summary>
                            <ul style="margin-top: 5px;">
                                ${error.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                            </ul>
                        </details>

                        ${error.screenshot ? `
                            <details style="margin-top: 10px;">
                                <summary style="cursor: pointer; color: #721c24; font-weight: bold;">📸 失败时截图</summary>
                                <img src="${error.screenshot}" alt="失败截图" style="max-width: 100%; margin-top: 5px; border: 1px solid #ddd; border-radius: 4px;">
                            </details>
                        ` : ''}
                    </div>
                `;
            });

            document.getElementById('resultContent').innerHTML = errorHtml;
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').className = 'results error';
        }

        // 显示配置示例
        function showConfigExample() {
            const config = collectConfiguration();
            const exampleCode = `// 使用示例代码
const requestValidation = ${JSON.stringify(config.requestValidation, null, 2)};

const requestOrchestration = ${JSON.stringify(config.requestOrchestration, null, 2)};

// API调用
const response = await fetch('/api/automation/execute-with-validation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        steps: ${JSON.stringify(config.steps, null, 2)},
        requestValidation,
        requestOrchestration
    })
});`;

            document.getElementById('exampleCode').textContent = exampleCode;
            document.getElementById('configExample').style.display = 'block';
        }

        // 显示结果
        function showResults(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('resultContent');

            resultsDiv.style.display = 'block';
            resultsDiv.className = `results ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            contentDiv.innerHTML += `<div><strong>[${timestamp}]</strong> ${message}</div>`;

            // 滚动到底部
            contentDiv.scrollTop = contentDiv.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            // 停止所有轮询
            stopStatusPolling();

            // 清空执行ID
            currentExecutionId = null;

            // 清空界面
            document.getElementById('results').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';
            document.getElementById('configExample').style.display = 'none';

            // 重置按钮状态
            const executeBtn = document.getElementById('executeBtn');
            executeBtn.disabled = false;
            executeBtn.textContent = '执行带验证的测试';

            console.log('已清空所有结果和状态');
        }
    </script>
</body>
</html>
