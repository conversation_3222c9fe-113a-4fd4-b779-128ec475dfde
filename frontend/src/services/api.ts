import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 60000, // 60秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.error || response.data.message || 'Unknown error');
    }
  },
  (error) => {
    // 统一处理错误
    const message = error.response?.data?.error || 
                   error.response?.data?.message || 
                   error.message || 
                   'Network error';
    throw new Error(message);
  }
);

// API接口定义
export const automationApi = {
  // 执行自动化测试
  executeTest: async (data: {
    steps: string[];
    testCaseId?: string;
    config?: {
      aiProvider?: {
        name: 'openai' | 'anthropic';
        model: string;
        apiKey: string;
      };
      browserConfig?: {
        headless?: boolean;
        viewport?: {
          width?: number;
          height?: number;
        };
        timeout?: number;
      };
      saveScreenshots?: boolean;
      generateReport?: boolean;
    };
  }) => {
    return api.post('/automation/execute', data);
  },

  // 解析自然语言步骤
  parseSteps: async (data: { steps: string[] }) => {
    return api.post('/automation/parse', data);
  },

  // 获取执行状态
  getExecutionStatus: async (executionId: string) => {
    return api.get(`/automation/execution/${executionId}`);
  },

  // 取消执行
  cancelExecution: async (executionId: string) => {
    return api.post(`/automation/execution/${executionId}/cancel`);
  },

  // 获取执行历史
  getExecutionHistory: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
  }) => {
    return api.get('/automation/executions', { params });
  },

  // 获取测试报告
  getTestReport: async (executionId: string) => {
    return api.get(`/automation/execution/${executionId}/report`);
  },

  // 获取执行截图
  getExecutionScreenshots: async (executionId: string) => {
    return api.get(`/automation/execution/${executionId}/screenshots`);
  },

  // 健康检查
  healthCheck: async () => {
    return api.get('/automation/health');
  },
};

export default api;
