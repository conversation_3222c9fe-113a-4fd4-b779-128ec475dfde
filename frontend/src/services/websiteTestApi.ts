import axios from 'axios';
import type { 
  WebsiteTestConfig, 
  WebsiteTestResponse, 
  WebsiteTestResult,
  TestHistoryQuery,
  ApiResponse 
} from '@shared/types/websiteTest';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 60000, // 60秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response.data;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.response?.data || error.message);
    
    // 统一错误处理
    if (error.response?.status === 401) {
      // 处理未授权错误
      console.warn('Unauthorized access');
    } else if (error.response?.status >= 500) {
      // 处理服务器错误
      console.error('Server error occurred');
    }
    
    return Promise.reject(error);
  }
);

// 网站测试API接口
export const websiteTestApi = {
  /**
   * 执行网站测试
   */
  executeTest: async (config: WebsiteTestConfig): Promise<WebsiteTestResponse> => {
    return api.post('/website-test/execute', config);
  },

  /**
   * 获取测试结果
   */
  getTestResult: async (testResultId: string): Promise<ApiResponse<WebsiteTestResult>> => {
    return api.get(`/website-test/result/${testResultId}`);
  },

  /**
   * 获取测试历史
   */
  getTestHistory: async (query: TestHistoryQuery = {}): Promise<ApiResponse<{
    tests: WebsiteTestResult[];
    pagination: {
      page: number;
      limit: number;
      total: number;
    };
  }>> => {
    const params = new URLSearchParams();
    
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    if (query.websiteUrl) params.append('websiteUrl', query.websiteUrl);
    if (query.testType) params.append('testType', query.testType);
    if (query.status) params.append('status', query.status);
    if (query.startDate) params.append('startDate', query.startDate.toISOString());
    if (query.endDate) params.append('endDate', query.endDate.toISOString());
    if (query.sortBy) params.append('sortBy', query.sortBy);
    if (query.sortOrder) params.append('sortOrder', query.sortOrder);

    return api.get(`/website-test/history?${params.toString()}`);
  },

  /**
   * 停止测试
   */
  stopTest: async (testResultId: string): Promise<ApiResponse> => {
    return api.post(`/website-test/stop/${testResultId}`);
  },

  /**
   * 获取测试统计信息
   */
  getTestStatistics: async (): Promise<ApiResponse<{
    totalTests: number;
    completedTests: number;
    failedTests: number;
    runningTests: number;
    averageDuration: number;
    totalLinksChecked: number;
    totalErrorsFound: number;
    mostTestedWebsite?: {
      url: string;
      count: number;
    };
    recentActivity?: {
      date: string;
      count: number;
    }[];
  }>> => {
    return api.get('/website-test/statistics');
  },

  /**
   * 健康检查
   */
  healthCheck: async (): Promise<ApiResponse<{
    status: string;
    database: string;
    timestamp: Date;
    uptime: number;
  }>> => {
    return api.get('/website-test/health');
  },

  /**
   * 专门为webopte.com设计的快速测试
   */
  executeWebopteTest: async (): Promise<WebsiteTestResponse> => {
    return api.post('/website-test/webopte');
  },

  /**
   * 批量测试多个网站
   */
  executeBatchTest: async (data: {
    websites: string[];
    config: Omit<WebsiteTestConfig, 'websiteUrl'>;
    concurrent?: number;
  }): Promise<ApiResponse<{
    successful: Array<{
      websiteUrl: string;
      success: boolean;
      testResultId: string;
    }>;
    failed: Array<{
      websiteUrl: string;
      success: boolean;
      error: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }>> => {
    return api.post('/website-test/batch', data);
  },

  /**
   * 获取测试详细报告（如果后续实现）
   */
  getTestReport: async (testResultId: string, format: 'json' | 'pdf' | 'html' | 'csv' = 'json'): Promise<any> => {
    return api.get(`/website-test/report/${testResultId}?format=${format}`);
  },

  /**
   * 导出测试结果
   */
  exportTestResults: async (query: TestHistoryQuery & { format: 'csv' | 'excel' | 'json' }): Promise<Blob> => {
    const params = new URLSearchParams();
    
    if (query.page) params.append('page', query.page.toString());
    if (query.limit) params.append('limit', query.limit.toString());
    if (query.websiteUrl) params.append('websiteUrl', query.websiteUrl);
    if (query.testType) params.append('testType', query.testType);
    if (query.status) params.append('status', query.status);
    if (query.startDate) params.append('startDate', query.startDate.toISOString());
    if (query.endDate) params.append('endDate', query.endDate.toISOString());
    if (query.format) params.append('format', query.format);

    const response = await api.get(`/website-test/export?${params.toString()}`, {
      responseType: 'blob'
    });
    
    return response;
  },

  /**
   * 获取实时测试状态（WebSocket连接，如果后续实现）
   */
  subscribeToTestStatus: (testResultId: string, callback: (status: any) => void): (() => void) => {
    // 这里可以实现WebSocket连接来获取实时状态更新
    // 暂时返回一个空的取消订阅函数
    console.log('WebSocket subscription not implemented yet');
    return () => {};
  },

  /**
   * 验证网站URL是否可访问
   */
  validateWebsiteUrl: async (url: string): Promise<ApiResponse<{
    accessible: boolean;
    status?: number;
    responseTime?: number;
    error?: string;
  }>> => {
    return api.post('/website-test/validate-url', { url });
  },

  /**
   * 获取网站基本信息（标题、描述等）
   */
  getWebsiteInfo: async (url: string): Promise<ApiResponse<{
    title: string;
    description?: string;
    favicon?: string;
    language?: string;
    charset?: string;
  }>> => {
    return api.post('/website-test/website-info', { url });
  }
};

// 导出类型
export type WebsiteTestApiType = typeof websiteTestApi;

// 默认导出
export default websiteTestApi;
