import React from 'react';
import { Layout as AntLayout, Menu, Typography } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { PlayCircleOutlined, HistoryOutlined, RobotOutlined, GlobalOutlined } from '@ant-design/icons';

const { Header, Content, Sider } = AntLayout;
const { Title } = Typography;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <PlayCircleOutlined />,
      label: <Link to="/">测试执行</Link>,
    },
    {
      key: '/history',
      icon: <HistoryOutlined />,
      label: <Link to="/history">执行历史</Link>,
    },
    {
      key: '/website-test',
      icon: <GlobalOutlined />,
      label: <Link to="/website-test">网站测试</Link>,
    },
  ];

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        background: '#001529',
        padding: '0 24px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          color: 'white',
          fontSize: '20px',
          fontWeight: 'bold'
        }}>
          <RobotOutlined style={{ marginRight: '12px', fontSize: '24px' }} />
          AI浏览器自动化测试平台
        </div>
      </Header>

      <AntLayout>
        <Sider
          width={250}
          style={{ background: '#fff' }}
          breakpoint="lg"
          collapsedWidth="0"
        >
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            style={{ height: '100%', borderRight: 0 }}
            items={menuItems}
          />
        </Sider>

        <AntLayout style={{ padding: '24px' }}>
          <Content
            style={{
              background: '#fff',
              padding: '24px',
              margin: 0,
              minHeight: 280,
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}
          >
            {children}
          </Content>
        </AntLayout>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
