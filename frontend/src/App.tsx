import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import TestRunner from './pages/TestRunner';
import TestHistory from './pages/TestHistory';
import WebsiteTest from './pages/WebsiteTest';
import './App.css';

// 创建QueryClient实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider locale={zhCN}>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<TestRunner />} />
              <Route path="/history" element={<TestHistory />} />
              <Route path="/website-test" element={<WebsiteTest />} />
            </Routes>
          </Layout>
          <Toaster position="top-right" />
        </Router>
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
