import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Space, 
  Typography, 
  List, 
  Tag, 
  Progress,
  Alert,
  Divider,
  Switch,
  Row,
  Col
} from 'antd';
import { 
  PlayCircleOutlined, 
  PlusOutlined, 
  DeleteOutlined,
  StopOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { automationApi } from '../services/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface TestStep {
  id: string;
  description: string;
}

const TestRunner: React.FC = () => {
  const [steps, setSteps] = useState<TestStep[]>([]);
  const [currentStep, setCurrentStep] = useState('');
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [saveScreenshots, setSaveScreenshots] = useState(true);
  const [generateReport, setGenerateReport] = useState(true);

  // 添加测试步骤
  const addStep = () => {
    if (currentStep.trim()) {
      const newStep: TestStep = {
        id: Date.now().toString(),
        description: currentStep.trim(),
      };
      setSteps([...steps, newStep]);
      setCurrentStep('');
    }
  };

  // 删除测试步骤
  const removeStep = (id: string) => {
    setSteps(steps.filter(step => step.id !== id));
  };

  // 执行测试
  const executeMutation = useMutation({
    mutationFn: automationApi.executeTest,
    onSuccess: (data) => {
      setExecutionId(data.executionId);
      toast.success('测试开始执行！');
    },
    onError: (error: any) => {
      toast.error(`执行失败: ${error.message}`);
    },
  });

  // 获取执行状态
  const { data: executionStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['execution-status', executionId],
    queryFn: () => automationApi.getExecutionStatus(executionId!),
    enabled: !!executionId,
    refetchInterval: executionId ? 2000 : false, // 每2秒刷新一次
  });

  // 取消执行
  const cancelMutation = useMutation({
    mutationFn: () => automationApi.cancelExecution(executionId!),
    onSuccess: () => {
      setExecutionId(null);
      toast.success('测试已取消');
    },
    onError: (error: any) => {
      toast.error(`取消失败: ${error.message}`);
    },
  });

  const handleExecute = () => {
    if (steps.length === 0) {
      toast.error('请至少添加一个测试步骤');
      return;
    }

    const stepDescriptions = steps.map(step => step.description);
    executeMutation.mutate({
      steps: stepDescriptions,
      config: {
        saveScreenshots,
        generateReport,
      },
    });
  };

  const handleCancel = () => {
    cancelMutation.mutate();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'running': return 'processing';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const getProgress = () => {
    if (!executionStatus?.results) return 0;
    const total = executionStatus.results.length;
    const completed = executionStatus.results.filter(
      r => r.status === 'passed' || r.status === 'failed'
    ).length;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  return (
    <div>
      <Title level={2}>AI自动化测试执行</Title>
      <Text type="secondary">
        输入自然语言描述的测试步骤，AI将自动执行浏览器操作
      </Text>

      <Divider />

      <Row gutter={24}>
        <Col span={12}>
          <Card title="测试步骤配置" style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <TextArea
                placeholder="请输入测试步骤，例如：打开百度首页"
                value={currentStep}
                onChange={(e) => setCurrentStep(e.target.value)}
                onPressEnter={addStep}
                rows={3}
              />
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={addStep}
                disabled={!currentStep.trim()}
              >
                添加步骤
              </Button>

              <div style={{ marginTop: 16 }}>
                <Text strong>执行选项：</Text>
                <div style={{ marginTop: 8 }}>
                  <Space direction="vertical">
                    <div>
                      <Switch 
                        checked={saveScreenshots} 
                        onChange={setSaveScreenshots} 
                      />
                      <span style={{ marginLeft: 8 }}>保存截图</span>
                    </div>
                    <div>
                      <Switch 
                        checked={generateReport} 
                        onChange={setGenerateReport} 
                      />
                      <span style={{ marginLeft: 8 }}>生成测试报告</span>
                    </div>
                  </Space>
                </div>
              </div>
            </Space>
          </Card>

          <Card title={`测试步骤列表 (${steps.length})`}>
            {steps.length === 0 ? (
              <Text type="secondary">暂无测试步骤</Text>
            ) : (
              <List
                dataSource={steps}
                renderItem={(step, index) => (
                  <List.Item
                    actions={[
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeStep(step.id)}
                      />
                    ]}
                  >
                    <div style={{ width: '100%' }}>
                      <Text strong>{index + 1}. </Text>
                      <Text>{step.description}</Text>
                    </div>
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>

        <Col span={12}>
          <Card title="执行控制">
            <Space direction="vertical" style={{ width: '100%' }}>
              {!executionId ? (
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handleExecute}
                  loading={executeMutation.isPending}
                  disabled={steps.length === 0}
                  style={{ width: '100%' }}
                >
                  开始执行测试
                </Button>
              ) : (
                <Button
                  danger
                  size="large"
                  icon={<StopOutlined />}
                  onClick={handleCancel}
                  loading={cancelMutation.isPending}
                  style={{ width: '100%' }}
                >
                  停止执行
                </Button>
              )}

              {executionStatus && (
                <div style={{ marginTop: 16 }}>
                  <Alert
                    message={`执行状态: ${executionStatus.status}`}
                    type={getStatusColor(executionStatus.status) as any}
                    showIcon
                  />
                  
                  <div style={{ marginTop: 16 }}>
                    <Text strong>执行进度:</Text>
                    <Progress 
                      percent={getProgress()} 
                      status={executionStatus.status === 'failed' ? 'exception' : 'active'}
                    />
                  </div>

                  {executionStatus.results && executionStatus.results.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      <Text strong>步骤详情:</Text>
                      <List
                        size="small"
                        dataSource={executionStatus.results}
                        renderItem={(result, index) => (
                          <List.Item>
                            <div style={{ width: '100%' }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Text>步骤 {index + 1}</Text>
                                <Tag color={getStatusColor(result.status)}>
                                  {result.status}
                                </Tag>
                              </div>
                              {result.error && (
                                <Text type="danger" style={{ fontSize: '12px' }}>
                                  {result.error}
                                </Text>
                              )}
                            </div>
                          </List.Item>
                        )}
                      />
                    </div>
                  )}
                </div>
              )}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TestRunner;
