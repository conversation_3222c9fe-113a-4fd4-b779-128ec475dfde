import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Switch,
  InputNumber,
  Space,
  Alert,
  Table,
  Tag,
  Progress,
  Modal,
  Descriptions,
  Typography,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EyeOutlined,
  DownloadOutlined,
  BugOutlined,
  LinkOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { websiteTestApi } from '../services/websiteTestApi';
import type { WebsiteTestConfig, WebsiteTestResult, TestStatus } from '@shared/types/websiteTest';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface WebsiteTestPageState {
  isTestRunning: boolean;
  currentTestId: string | null;
  showResultModal: boolean;
  selectedResult: WebsiteTestResult | null;
}

const WebsiteTest: React.FC = () => {
  const [form] = Form.useForm();
  const [state, setState] = useState<WebsiteTestPageState>({
    isTestRunning: false,
    currentTestId: null,
    showResultModal: false,
    selectedResult: null
  });

  const queryClient = useQueryClient();

  // 获取测试历史
  const { data: testHistory, isLoading: historyLoading, refetch: refetchHistory } = useQuery({
    queryKey: ['websiteTestHistory'],
    queryFn: () => websiteTestApi.getTestHistory({ page: 1, limit: 20 }),
    refetchInterval: 5000 // 每5秒刷新一次
  });

  // 获取统计信息
  const { data: statistics } = useQuery({
    queryKey: ['websiteTestStatistics'],
    queryFn: () => websiteTestApi.getTestStatistics(),
    refetchInterval: 30000 // 每30秒刷新一次
  });

  // 执行测试的mutation
  const executeTestMutation = useMutation({
    mutationFn: websiteTestApi.executeTest,
    onSuccess: (data) => {
      if (data.success && data.data) {
        setState(prev => ({
          ...prev,
          isTestRunning: true,
          currentTestId: data.data!.testResultId
        }));
        queryClient.invalidateQueries({ queryKey: ['websiteTestHistory'] });
      }
    },
    onError: (error) => {
      console.error('Test execution failed:', error);
    }
  });

  // 停止测试的mutation
  const stopTestMutation = useMutation({
    mutationFn: (testId: string) => websiteTestApi.stopTest(testId),
    onSuccess: () => {
      setState(prev => ({
        ...prev,
        isTestRunning: false,
        currentTestId: null
      }));
      queryClient.invalidateQueries({ queryKey: ['websiteTestHistory'] });
    }
  });

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    const config: WebsiteTestConfig = {
      websiteUrl: values.websiteUrl,
      testType: values.testType,
      browserConfig: {
        headless: values.headless,
        viewport: {
          width: values.viewportWidth,
          height: values.viewportHeight
        },
        timeout: values.timeout,
        userAgent: values.userAgent
      },
      crawlerConfig: values.testType === 'full_test' ? {
        maxDepth: values.maxDepth,
        maxPages: values.maxPages,
        followExternalLinks: values.followExternalLinks,
        respectRobotsTxt: values.respectRobotsTxt,
        delay: values.delay,
        concurrent: values.concurrent
      } : undefined,
      errorMonitorConfig: {
        captureScreenshots: values.captureScreenshots,
        captureConsoleLogs: values.captureConsoleLogs,
        captureNetworkErrors: values.captureNetworkErrors,
        monitorJavaScriptErrors: values.monitorJavaScriptErrors
      }
    };

    executeTestMutation.mutate(config);
  };

  // 快速测试webopte.com
  const handleWebopteTest = () => {
    websiteTestApi.executeWebopteTest().then((data) => {
      if (data.success && data.data) {
        setState(prev => ({
          ...prev,
          isTestRunning: true,
          currentTestId: data.data!.testResultId
        }));
        queryClient.invalidateQueries({ queryKey: ['websiteTestHistory'] });
      }
    });
  };

  // 停止测试
  const handleStopTest = () => {
    if (state.currentTestId) {
      stopTestMutation.mutate(state.currentTestId);
    }
  };

  // 查看测试结果
  const handleViewResult = async (testResult: WebsiteTestResult) => {
    setState(prev => ({
      ...prev,
      showResultModal: true,
      selectedResult: testResult
    }));
  };

  // 获取状态标签颜色
  const getStatusColor = (status: TestStatus) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'processing';
      case 'failed': return 'error';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  // 获取测试类型标签
  const getTestTypeTag = (testType: string) => {
    switch (testType) {
      case 'link_check': return <Tag color="blue" icon={<LinkOutlined />}>链接检查</Tag>;
      case 'error_monitor': return <Tag color="orange" icon={<BugOutlined />}>错误监控</Tag>;
      case 'full_test': return <Tag color="green" icon={<GlobalOutlined />}>完整测试</Tag>;
      default: return <Tag>{testType}</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '网站URL',
      dataIndex: 'websiteUrl',
      key: 'websiteUrl',
      render: (url: string) => (
        <a href={url} target="_blank" rel="noopener noreferrer">
          {url.length > 50 ? `${url.substring(0, 50)}...` : url}
        </a>
      )
    },
    {
      title: '测试类型',
      dataIndex: 'testType',
      key: 'testType',
      render: (type: string) => getTestTypeTag(type)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: TestStatus) => (
        <Tag color={getStatusColor(status)}>
          {status === 'completed' ? '已完成' :
           status === 'running' ? '运行中' :
           status === 'failed' ? '失败' : '等待中'}
        </Tag>
      )
    },
    {
      title: '链接数量',
      dataIndex: 'totalLinks',
      key: 'totalLinks',
      render: (total: number, record: WebsiteTestResult) => (
        <Space>
          <Text>{total}</Text>
          {record.failedLinks > 0 && (
            <Text type="danger">({record.failedLinks} 失败)</Text>
          )}
        </Space>
      )
    },
    {
      title: '错误数量',
      key: 'errors',
      render: (record: WebsiteTestResult) => (
        <Space>
          {record.serverErrors > 0 && (
            <Tag color="red">服务端: {record.serverErrors}</Tag>
          )}
          {record.frontendErrors > 0 && (
            <Tag color="orange">前端: {record.frontendErrors}</Tag>
          )}
          {record.serverErrors === 0 && record.frontendErrors === 0 && (
            <Tag color="green">无错误</Tag>
          )}
        </Space>
      )
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: WebsiteTestResult) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewResult(record)}
          >
            查看
          </Button>
          {record.status === 'running' && (
            <Button
              type="link"
              danger
              icon={<StopOutlined />}
              onClick={() => stopTestMutation.mutate(record.id)}
            >
              停止
            </Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>网站测试</Title>
      
      {/* 统计信息 */}
      {statistics?.data && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总测试数" value={statistics.data.totalTests} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="成功率" 
                value={statistics.data.totalTests > 0 ? 
                  ((statistics.data.completedTests / statistics.data.totalTests) * 100).toFixed(1) : 0
                } 
                suffix="%" 
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="检查链接数" value={statistics.data.totalLinksChecked} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="发现错误数" value={statistics.data.totalErrorsFound} />
            </Card>
          </Col>
        </Row>
      )}

      <Row gutter={24}>
        {/* 左侧：测试配置 */}
        <Col span={8}>
          <Card title="测试配置" style={{ marginBottom: 24 }}>
            {/* 快速测试按钮 */}
            <Space style={{ marginBottom: 16, width: '100%' }} direction="vertical">
              <Button
                type="primary"
                icon={<GlobalOutlined />}
                onClick={handleWebopteTest}
                loading={executeTestMutation.isPending}
                block
              >
                快速测试 webopte.com
              </Button>
              
              {state.isTestRunning && (
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={handleStopTest}
                  loading={stopTestMutation.isPending}
                  block
                >
                  停止当前测试
                </Button>
              )}
            </Space>

            <Divider>或自定义测试</Divider>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                testType: 'full_test',
                headless: true,
                viewportWidth: 1920,
                viewportHeight: 1080,
                timeout: 30000,
                maxDepth: 3,
                maxPages: 50,
                followExternalLinks: false,
                respectRobotsTxt: true,
                delay: 1000,
                concurrent: 3,
                captureScreenshots: true,
                captureConsoleLogs: true,
                captureNetworkErrors: true,
                monitorJavaScriptErrors: true
              }}
            >
              <Form.Item
                name="websiteUrl"
                label="网站URL"
                rules={[
                  { required: true, message: '请输入网站URL' },
                  { type: 'url', message: '请输入有效的URL' }
                ]}
              >
                <Input placeholder="https://example.com" />
              </Form.Item>

              <Form.Item name="testType" label="测试类型">
                <Select>
                  <Option value="link_check">链接检查</Option>
                  <Option value="error_monitor">错误监控</Option>
                  <Option value="full_test">完整测试</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={executeTestMutation.isPending}
                  disabled={state.isTestRunning}
                  block
                >
                  开始测试
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 右侧：测试历史 */}
        <Col span={16}>
          <Card 
            title="测试历史" 
            extra={
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => refetchHistory()}
                loading={historyLoading}
              >
                刷新
              </Button>
            }
          >
            <Table
              columns={columns}
              dataSource={testHistory?.data?.tests || []}
              rowKey="id"
              loading={historyLoading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 测试结果详情模态框 */}
      <Modal
        title="测试结果详情"
        open={state.showResultModal}
        onCancel={() => setState(prev => ({ ...prev, showResultModal: false, selectedResult: null }))}
        footer={null}
        width={800}
      >
        {state.selectedResult && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="网站URL" span={2}>
              <a href={state.selectedResult.websiteUrl} target="_blank" rel="noopener noreferrer">
                {state.selectedResult.websiteUrl}
              </a>
            </Descriptions.Item>
            <Descriptions.Item label="测试类型">
              {getTestTypeTag(state.selectedResult.testType)}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={getStatusColor(state.selectedResult.status)}>
                {state.selectedResult.status}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="总链接数">
              {state.selectedResult.totalLinks}
            </Descriptions.Item>
            <Descriptions.Item label="成功链接数">
              {state.selectedResult.successfulLinks}
            </Descriptions.Item>
            <Descriptions.Item label="失败链接数">
              {state.selectedResult.failedLinks}
            </Descriptions.Item>
            <Descriptions.Item label="服务端错误">
              {state.selectedResult.serverErrors}
            </Descriptions.Item>
            <Descriptions.Item label="前端错误">
              {state.selectedResult.frontendErrors}
            </Descriptions.Item>
            <Descriptions.Item label="开始时间">
              {new Date(state.selectedResult.startTime).toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="结束时间">
              {state.selectedResult.endTime ? 
                new Date(state.selectedResult.endTime).toLocaleString() : 
                '未完成'
              }
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default WebsiteTest;
