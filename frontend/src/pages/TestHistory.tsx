import React from 'react';
import { 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Typography,
  Tooltip,
  Empty
} from 'antd';
import { 
  EyeOutlined, 
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { automationApi } from '../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;

const TestHistory: React.FC = () => {
  const { data: executions, isLoading, refetch } = useQuery({
    queryKey: ['execution-history'],
    queryFn: () => automationApi.getExecutionHistory(),
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'running': return 'processing';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: '执行ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <Tooltip title={id}>
          <span style={{ fontFamily: 'monospace' }}>
            {id.substring(0, 8)}...
          </span>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 180,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 180,
      render: (time: string) => 
        time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '执行时长',
      key: 'duration',
      width: 120,
      render: (record: any) => {
        if (!record.endTime) return '-';
        const duration = dayjs(record.endTime).diff(dayjs(record.startTime), 'second');
        return `${duration}秒`;
      },
    },
    {
      title: '步骤数',
      dataIndex: 'results',
      key: 'stepCount',
      width: 100,
      render: (results: any[]) => results?.length || 0,
    },
    {
      title: '成功率',
      dataIndex: 'results',
      key: 'successRate',
      width: 100,
      render: (results: any[]) => {
        if (!results || results.length === 0) return '-';
        const passed = results.filter(r => r.status === 'passed').length;
        const rate = Math.round((passed / results.length) * 100);
        return `${rate}%`;
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: any) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record.id)}
            />
          </Tooltip>
          <Tooltip title="下载报告">
            <Button 
              type="text" 
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadReport(record.id)}
              disabled={record.status !== 'completed'}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleViewDetails = (executionId: string) => {
    // 这里可以打开详情模态框或跳转到详情页面
    console.log('View details for:', executionId);
  };

  const handleDownloadReport = (executionId: string) => {
    // 这里可以下载测试报告
    console.log('Download report for:', executionId);
  };

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: 24 
      }}>
        <Title level={2}>执行历史</Title>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={() => refetch()}
          loading={isLoading}
        >
          刷新
        </Button>
      </div>

      <Card>
        {executions && executions.length > 0 ? (
          <Table
            columns={columns}
            dataSource={executions}
            rowKey="id"
            loading={isLoading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            scroll={{ x: 1000 }}
          />
        ) : (
          <Empty 
            description="暂无执行记录"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Card>
    </div>
  );
};

export default TestHistory;
