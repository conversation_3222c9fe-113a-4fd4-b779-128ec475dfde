// 共享类型定义

export interface TestStep {
  id: string;
  description: string;
  action: string;
  target?: string;
  value?: string;
  expectedResult?: string;
  order: number;
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  steps: TestStep[];
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
}

export interface TestExecution {
  id: string;
  testCaseId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  results: TestStepResult[];
  screenshots: string[];
  logs: string[];
  error?: string;
}

export interface TestStepResult {
  stepId: string;
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
  startTime: Date;
  endTime?: Date;
  screenshot?: string;
  error?: string;
  actualResult?: string;
  logs: string[];
}

export interface AIProvider {
  name: 'openai' | 'anthropic';
  model: string;
  apiKey: string;
}

export interface BrowserConfig {
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
  timeout: number;
  userAgent?: string;
}

export interface AutomationRequest {
  testCaseId?: string;
  steps: string[];
  config?: {
    aiProvider?: AIProvider;
    browserConfig?: BrowserConfig;
    saveScreenshots?: boolean;
    generateReport?: boolean;
  };
}

export interface AutomationResponse {
  executionId: string;
  status: 'started' | 'completed' | 'failed';
  message: string;
  results?: TestExecution;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 自然语言处理相关类型
export interface ParsedStep {
  originalText: string;
  action: ActionType;
  target?: string;
  value?: string;
  confidence: number;
  suggestions?: string[];
}

export enum ActionType {
  NAVIGATE = 'navigate',
  CLICK = 'click',
  TYPE = 'type',
  SELECT = 'select',
  WAIT = 'wait',
  VERIFY = 'verify',
  EXTRACT = 'extract',
  SCROLL = 'scroll',
  HOVER = 'hover',
  SCREENSHOT = 'screenshot',
  CUSTOM = 'custom'
}

// 错误类型
export interface AutomationError {
  code: string;
  message: string;
  step?: string;
  screenshot?: string;
  timestamp: Date;
}

// URL请求验证相关类型
export interface RequestValidationRule {
  // 基本验证规则
  expectedStatusCode?: number | number[]; // 期望的HTTP状态码
  expectedContentType?: string; // 期望的内容类型
  maxResponseTime?: number; // 最大响应时间(ms)

  // 响应数据验证
  expectedResultCount?: {
    min?: number; // 最小结果数量
    max?: number; // 最大结果数量
    exact?: number; // 精确结果数量
  };

  // 响应内容验证
  responseValidation?: {
    jsonPath?: string; // JSON路径表达式
    expectedValue?: any; // 期望值
    contains?: string; // 包含的文本
    regex?: string; // 正则表达式
  };

  // 自定义验证函数
  customValidator?: string; // 自定义验证函数名称
}

export interface RequestValidationResult {
  url: string;
  method: string;
  statusCode: number;
  responseTime: number;
  contentType?: string;
  resultCount?: number;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  responseData?: any;
  timestamp: Date;
}

// 请求编排相关类型
export interface RequestOrchestrationRule {
  id: string;
  name: string;
  description?: string;

  // 触发条件
  trigger: {
    type: 'url_success' | 'url_failure' | 'condition' | 'manual';
    sourceUrl?: string; // 触发源URL
    condition?: string; // 自定义条件表达式
  };

  // 目标请求
  targetRequest: {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    body?: any;
    validation?: RequestValidationRule;
  };

  // 执行配置
  execution: {
    delay?: number; // 延迟执行时间(ms)
    retries?: number; // 重试次数
    timeout?: number; // 超时时间(ms)
    continueOnFailure?: boolean; // 失败时是否继续
  };

  // 数据传递
  dataMapping?: {
    from: string; // 源数据路径
    to: string; // 目标数据路径
  }[];
}

export interface RequestOrchestrationContext {
  executionId: string;
  currentStep: number;
  totalSteps: number;
  variables: Record<string, any>; // 上下文变量
  results: RequestValidationResult[]; // 历史结果
  errors: string[];
}

// 网络监控相关类型
export interface NetworkRequest {
  id: string;
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  timestamp: Date;
  status?: 'pending' | 'completed' | 'failed';
}

export interface NetworkResponse {
  requestId: string;
  statusCode: number;
  headers: Record<string, string>;
  body?: any;
  responseTime: number;
  timestamp: Date;
}

// 配置类型
export interface AppConfig {
  server: {
    port: number;
    corsOrigin: string;
  };
  ai: {
    defaultProvider: 'openai' | 'anthropic';
    defaultModel: string;
    timeout: number;
    maxRetries: number;
  };
  browser: {
    headless: boolean;
    timeout: number;
    viewport: {
      width: number;
      height: number;
    };
  };
  storage: {
    databasePath: string;
    screenshotDir: string;
    uploadDir: string;
  };
  // 新增MySQL配置
  mysql: {
    host: string;
    port: number;
    user: string;
    password: string;
    database: string;
    connectionLimit: number;
    acquireTimeout: number;
    timeout: number;
    reconnect: boolean;
    charset: string;
  };
}
