// 网站测试相关类型定义

// 测试类型枚举
export type TestType = 'link_check' | 'error_monitor' | 'full_test';

// 测试状态枚举
export type TestStatus = 'pending' | 'running' | 'completed' | 'failed';

// 错误类型枚举
export type ErrorType = 'server_error' | 'frontend_error' | 'network_error' | 'timeout_error';

// 网站测试配置
export interface WebsiteTestConfig {
  // 目标网站URL
  websiteUrl: string;
  // 测试类型
  testType: TestType;
  // 浏览器配置
  browserConfig?: {
    headless?: boolean;
    viewport?: {
      width?: number;
      height?: number;
    };
    timeout?: number;
    userAgent?: string;
  };
  // 爬虫配置
  crawlerConfig?: {
    maxDepth?: number;
    maxPages?: number;
    followExternalLinks?: boolean;
    respectRobotsTxt?: boolean;
    delay?: number;
    concurrent?: number;
  };
  // 错误监控配置
  errorMonitorConfig?: {
    captureScreenshots?: boolean;
    captureConsoleLogs?: boolean;
    captureNetworkErrors?: boolean;
    monitorJavaScriptErrors?: boolean;
  };
  // 通知配置
  notificationConfig?: {
    email?: string[];
    webhook?: string;
    slack?: string;
  };
}

// 网站测试结果
export interface WebsiteTestResult {
  id: string;
  websiteUrl: string;
  testType: TestType;
  status: TestStatus;
  startTime: Date;
  endTime?: Date;
  totalLinks: number;
  successfulLinks: number;
  failedLinks: number;
  serverErrors: number;
  frontendErrors: number;
  createdAt: Date;
  updatedAt: Date;
  // 可选的详细信息
  config?: WebsiteTestConfig;
  summary?: TestSummary;
}

// 测试摘要
export interface TestSummary {
  duration: number; // 测试持续时间（毫秒）
  averageResponseTime: number; // 平均响应时间
  slowestPage: {
    url: string;
    responseTime: number;
  };
  fastestPage: {
    url: string;
    responseTime: number;
  };
  errorBreakdown: {
    [key in ErrorType]: number;
  };
  statusCodeBreakdown: {
    [statusCode: string]: number;
  };
}

// 链接测试详情
export interface LinkTestDetail {
  id: string;
  testResultId: string;
  url: string;
  httpStatus?: number;
  responseTime?: number;
  errorMessage?: string;
  serverError: boolean;
  frontendError: boolean;
  screenshotPath?: string;
  testedAt: Date;
  createdAt: Date;
  // 额外信息
  redirectChain?: string[];
  contentType?: string;
  contentLength?: number;
  headers?: Record<string, string>;
  consoleErrors?: ConsoleError[];
  networkErrors?: NetworkError[];
}

// 控制台错误
export interface ConsoleError {
  level: 'error' | 'warning' | 'info' | 'debug';
  message: string;
  source?: string;
  line?: number;
  column?: number;
  timestamp: Date;
}

// 网络错误
export interface NetworkError {
  url: string;
  method: string;
  status?: number;
  errorMessage: string;
  timestamp: Date;
}

// 错误日志
export interface ErrorLog {
  id: string;
  testResultId?: string;
  linkDetailId?: string;
  errorType: ErrorType;
  errorCode?: string;
  errorMessage: string;
  stackTrace?: string;
  url?: string;
  userAgent?: string;
  createdAt: Date;
  // 上下文信息
  context?: {
    pageTitle?: string;
    referrer?: string;
    viewport?: {
      width: number;
      height: number;
    };
    timestamp: Date;
  };
}

// 发现的链接信息
export interface DiscoveredLink {
  url: string;
  text?: string;
  title?: string;
  type: 'internal' | 'external' | 'anchor' | 'resource';
  depth: number;
  sourceUrl: string;
  selector?: string;
  attributes?: Record<string, string>;
}

// 网站地图
export interface WebsiteSitemap {
  baseUrl: string;
  totalPages: number;
  internalLinks: DiscoveredLink[];
  externalLinks: DiscoveredLink[];
  resources: DiscoveredLink[];
  errors: string[];
  generatedAt: Date;
}

// 测试执行请求
export interface WebsiteTestRequest {
  config: WebsiteTestConfig;
  immediate?: boolean; // 是否立即执行
  scheduled?: Date; // 计划执行时间
}

// 测试执行响应
export interface WebsiteTestResponse {
  success: boolean;
  data?: {
    testResultId: string;
    status: TestStatus;
    message: string;
  };
  error?: string;
}

// 实时测试状态更新
export interface TestStatusUpdate {
  testResultId: string;
  status: TestStatus;
  progress: {
    current: number;
    total: number;
    percentage: number;
  };
  currentUrl?: string;
  message?: string;
  timestamp: Date;
}

// 测试历史查询参数
export interface TestHistoryQuery {
  page?: number;
  limit?: number;
  websiteUrl?: string;
  testType?: TestType;
  status?: TestStatus;
  startDate?: Date;
  endDate?: Date;
  sortBy?: 'createdAt' | 'startTime' | 'endTime' | 'duration';
  sortOrder?: 'asc' | 'desc';
}

// 测试统计信息
export interface TestStatistics {
  totalTests: number;
  completedTests: number;
  failedTests: number;
  runningTests: number;
  averageDuration: number;
  totalLinksChecked: number;
  totalErrorsFound: number;
  mostTestedWebsite: {
    url: string;
    count: number;
  };
  recentActivity: {
    date: string;
    count: number;
  }[];
}

// API响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: Date;
}

// 批量测试请求
export interface BatchTestRequest {
  websites: string[];
  config: Omit<WebsiteTestConfig, 'websiteUrl'>;
  concurrent?: number;
}

// 测试报告
export interface TestReport {
  testResultId: string;
  websiteUrl: string;
  generatedAt: Date;
  summary: TestSummary;
  details: LinkTestDetail[];
  errors: ErrorLog[];
  recommendations: string[];
  exportFormats: ('pdf' | 'html' | 'json' | 'csv')[];
}
