#!/bin/bash

# 网站测试服务启动脚本
echo "🚀 启动网站自动化测试服务..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

# 检查MySQL连接
echo "🔍 检查MySQL连接..."
npm run test-db

if [ $? -eq 0 ]; then
    echo "✅ MySQL连接成功"
else
    echo "⚠️  MySQL连接失败，将使用内存存储模式"
    echo "💡 如需使用MySQL，请检查配置并运行: npm run setup-db"
fi

# 启动服务
echo "🌐 启动网站测试服务..."
echo "📊 服务地址: http://localhost:3001"
echo "🎯 测试界面: http://localhost:3001/"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动简化版服务器
npx ts-node backend/simple-website-test-server.ts
