# AI Browser Automation Agent

基于浏览器的自动化测试AI智能体，只需输入文字步骤即可实现自动化测试。

## 特性

- 🤖 **自然语言驱动**: 使用简单的中文描述即可创建自动化测试
- 🎯 **智能识别**: 基于AI的页面元素识别和操作
- 📊 **可视化报告**: 自动生成测试报告和截图
- 🔄 **实时监控**: 实时查看测试执行状态
- 💾 **用例管理**: 保存和重用测试用例
- 🌐 **跨浏览器**: 支持Chrome、Firefox、Safari等主流浏览器

## 技术栈

- **前端**: React + TypeScript + Vite
- **后端**: Node.js + Express + TypeScript
- **自动化引擎**: Stagehand + Playwright
- **AI集成**: OpenAI GPT-4 / Anthropic Claude
- **数据存储**: SQLite

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装

1. 克隆项目
```bash
git clone <repository-url>
cd aitest
```

2. 安装依赖
```bash
npm install
```

3. 安装Playwright浏览器
```bash
npx playwright install
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，添加你的API密钥
```

5. 启动开发服务器
```bash
npm run dev
```

### 配置

在`.env`文件中配置以下必要参数：

```env
# AI模型API密钥（至少配置一个）
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 服务器配置
PORT=3001
NODE_ENV=development
```

## 使用方法

### 基本用法

1. 打开浏览器访问 `http://localhost:3000`
2. 在输入框中描述你想要执行的测试步骤，例如：
   - "打开百度首页"
   - "在搜索框中输入'人工智能'"
   - "点击搜索按钮"
   - "验证搜索结果包含'人工智能'"
3. 点击"执行测试"按钮
4. 查看实时执行状态和结果

### 高级功能

- **保存测试用例**: 将常用的测试步骤保存为用例
- **批量执行**: 一次执行多个测试用例
- **定时任务**: 设置定时执行测试
- **结果分析**: 查看详细的测试报告和趋势

## 项目结构

```
aitest/
├── frontend/              # React前端应用
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/               # Node.js后端服务
│   ├── controllers/       # 控制器
│   ├── services/          # 业务逻辑
│   ├── models/           # 数据模型
│   ├── routes/           # 路由定义
│   └── server.ts         # 服务器入口
├── shared/               # 共享类型定义
│   └── types/
├── tests/                # 测试文件
├── docs/                 # 项目文档
└── package.json          # 项目配置
```

## 开发

### 启动开发环境

```bash
# 启动后端和前端开发服务器
npm run dev

# 仅启动后端
npm run dev:backend

# 仅启动前端
npm run dev:frontend
```

### 运行测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch
```

### 代码检查

```bash
# 运行ESLint检查
npm run lint

# 自动修复代码风格问题
npm run lint:fix
```

## 部署

### 构建生产版本

```bash
npm run build
```

### 启动生产服务器

```bash
npm start
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 支持

如果你遇到任何问题，请：

1. 查看[文档](./docs/)
2. 搜索[Issues](../../issues)
3. 创建新的Issue描述问题
