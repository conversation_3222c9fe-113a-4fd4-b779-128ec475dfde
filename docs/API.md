# API 文档

## 概述

AI浏览器自动化测试平台提供RESTful API，支持自然语言驱动的浏览器自动化测试。

## 基础信息

- **Base URL**: `http://localhost:3001/api`
- **Content-Type**: `application/json`
- **认证**: 暂无（后续版本将添加）

## 响应格式

所有API响应都遵循统一格式：

```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "error": string // 仅在失败时存在
}
```

## 自动化测试 API

### 1. 执行自动化测试

**POST** `/automation/execute`

执行一系列自然语言描述的测试步骤。

#### 请求体

```json
{
  "steps": [
    "打开百度首页",
    "在搜索框中输入'人工智能'",
    "点击搜索按钮",
    "验证搜索结果包含'人工智能'"
  ],
  "testCaseId": "optional-test-case-id",
  "config": {
    "aiProvider": {
      "name": "openai",
      "model": "gpt-4",
      "apiKey": "your-api-key"
    },
    "browserConfig": {
      "headless": false,
      "viewport": {
        "width": 1920,
        "height": 1080
      },
      "timeout": 30000
    },
    "saveScreenshots": true,
    "generateReport": true
  }
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "executionId": "uuid-string",
    "status": "started",
    "message": "Test execution started successfully"
  },
  "message": "Test execution started"
}
```

### 2. 解析自然语言步骤

**POST** `/automation/parse`

解析和验证自然语言测试步骤。

#### 请求体

```json
{
  "steps": [
    "打开百度首页",
    "点击登录按钮"
  ]
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "parsedSteps": [
      {
        "originalText": "打开百度首页",
        "action": "navigate",
        "target": "百度",
        "confidence": 0.9,
        "suggestions": []
      }
    ],
    "validationResults": [
      {
        "step": {...},
        "validation": {
          "isValid": true,
          "errors": []
        }
      }
    ]
  }
}
```

### 3. 获取执行状态

**GET** `/automation/execution/{executionId}`

获取测试执行的实时状态。

#### 响应

```json
{
  "success": true,
  "data": {
    "id": "execution-id",
    "status": "running",
    "startTime": "2024-01-01T00:00:00.000Z",
    "results": [
      {
        "stepId": "step-id",
        "status": "passed",
        "startTime": "2024-01-01T00:00:00.000Z",
        "endTime": "2024-01-01T00:00:05.000Z",
        "screenshot": "/screenshots/step-screenshot.png",
        "logs": ["Step completed successfully"]
      }
    ],
    "screenshots": ["/screenshots/execution-screenshot.png"],
    "logs": ["Execution started"]
  }
}
```

### 4. 取消执行

**POST** `/automation/execution/{executionId}/cancel`

取消正在运行的测试执行。

#### 响应

```json
{
  "success": true,
  "message": "Execution cancelled successfully"
}
```

### 5. 获取执行历史

**GET** `/automation/executions`

获取历史执行记录。

#### 查询参数

- `page`: 页码（默认: 1）
- `limit`: 每页数量（默认: 20）
- `status`: 过滤状态（可选）

#### 响应

```json
{
  "success": true,
  "data": [
    {
      "id": "execution-id",
      "status": "completed",
      "startTime": "2024-01-01T00:00:00.000Z",
      "endTime": "2024-01-01T00:01:00.000Z",
      "results": [...],
      "screenshots": [...],
      "logs": [...]
    }
  ]
}
```

### 6. 健康检查

**GET** `/automation/health`

检查服务健康状态。

#### 响应

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "activeExecutions": 2,
    "uptime": 3600,
    "memory": {
      "rss": 123456789,
      "heapTotal": 123456789,
      "heapUsed": 123456789
    }
  }
}
```

## 测试用例管理 API

### 1. 创建测试用例

**POST** `/test-cases`

创建新的测试用例。

#### 请求体

```json
{
  "name": "百度搜索测试",
  "description": "测试百度搜索功能",
  "steps": [
    {
      "description": "打开百度首页",
      "action": "navigate",
      "target": "https://www.baidu.com",
      "order": 0
    },
    {
      "description": "在搜索框中输入关键词",
      "action": "type",
      "target": "搜索框",
      "value": "人工智能",
      "order": 1
    }
  ],
  "tags": ["搜索", "百度"]
}
```

### 2. 获取所有测试用例

**GET** `/test-cases`

### 3. 获取单个测试用例

**GET** `/test-cases/{id}`

### 4. 更新测试用例

**PUT** `/test-cases/{id}`

### 5. 删除测试用例

**DELETE** `/test-cases/{id}`

### 6. 执行测试用例

**POST** `/test-cases/{id}/execute`

## 错误代码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 400 | VALIDATION_ERROR | 请求参数验证失败 |
| 401 | UNAUTHORIZED | 未授权访问 |
| 403 | FORBIDDEN | 禁止访问 |
| 404 | NOT_FOUND | 资源不存在 |
| 408 | TIMEOUT | 请求超时 |
| 409 | CONFLICT | 资源冲突 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

## 使用示例

### JavaScript/TypeScript

```typescript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// 执行测试
const executeTest = async () => {
  try {
    const response = await api.post('/automation/execute', {
      steps: [
        '打开百度首页',
        '在搜索框中输入"测试"',
        '点击搜索按钮'
      ]
    });
    
    console.log('执行ID:', response.data.data.executionId);
  } catch (error) {
    console.error('执行失败:', error.response.data.error);
  }
};
```

### Python

```python
import requests

api_base = 'http://localhost:3001/api'

def execute_test():
    response = requests.post(f'{api_base}/automation/execute', json={
        'steps': [
            '打开百度首页',
            '在搜索框中输入"测试"',
            '点击搜索按钮'
        ]
    })
    
    if response.json()['success']:
        print('执行ID:', response.json()['data']['executionId'])
    else:
        print('执行失败:', response.json()['error'])
```

## 注意事项

1. **API密钥**: 使用AI功能需要配置OpenAI或Anthropic API密钥
2. **超时设置**: 复杂的自动化操作可能需要较长时间，建议设置合适的超时时间
3. **并发限制**: 同时执行的测试数量受服务器资源限制
4. **截图存储**: 截图文件会占用磁盘空间，建议定期清理
5. **日志记录**: 所有操作都会记录日志，便于问题排查
