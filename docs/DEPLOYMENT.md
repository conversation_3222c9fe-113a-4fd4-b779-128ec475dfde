# 部署指南

## 环境要求

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+), macOS (10.15+), Windows 10+
- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **内存**: 最少 4GB RAM，推荐 8GB+
- **磁盘**: 最少 10GB 可用空间

### 依赖服务
- **AI服务**: OpenAI API 或 Anthropic API
- **浏览器**: Chrome/Chromium (Playwright会自动安装)

## 本地开发部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd aitest
```

### 2. 安装依赖

```bash
# 安装后端依赖
npm install

# 安装前端依赖
cd frontend
npm install
cd ..

# 安装Playwright浏览器
npx playwright install
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

必需的环境变量：
```env
# AI模型API密钥（至少配置一个）
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 服务器配置
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# 数据库路径
DATABASE_PATH=./data/automation.db

# 其他配置
HEADLESS=false
BROWSER_TIMEOUT=30000
```

### 4. 启动服务

```bash
# 开发模式（同时启动前后端）
npm run dev

# 或分别启动
npm run dev:backend  # 后端服务
npm run dev:frontend # 前端服务
```

访问地址：
- 前端界面: http://localhost:3000
- 后端API: http://localhost:3001
- API文档: http://localhost:3001/api

## 生产环境部署

### 方式一：传统部署

#### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2（进程管理器）
sudo npm install -g pm2

# 安装必要的系统依赖
sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

#### 2. 部署应用

```bash
# 克隆代码
git clone <repository-url>
cd aitest

# 安装依赖
npm install
cd frontend && npm install && cd ..

# 安装浏览器
npx playwright install --with-deps

# 构建应用
npm run build

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置
```

#### 3. 配置PM2

创建 `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'ai-automation-backend',
    script: 'dist/backend/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

启动服务：

```bash
# 启动应用
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

#### 4. 配置Nginx反向代理

创建 `/etc/nginx/sites-available/ai-automation`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/aitest/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # 后端API
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 静态资源（截图、上传文件）
    location /screenshots {
        alias /path/to/aitest/screenshots;
    }

    location /uploads {
        alias /path/to/aitest/uploads;
    }
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/ai-automation /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 方式二：Docker部署

#### 1. 创建Dockerfile

```dockerfile
# 后端Dockerfile
FROM node:18-alpine

# 安装系统依赖
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# 设置Chromium路径
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# 安装依赖
RUN npm ci --only=production
RUN cd frontend && npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 安装Playwright
RUN npx playwright install --with-deps

EXPOSE 3001

CMD ["npm", "start"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  ai-automation:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/automation.db
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./data:/app/data
      - ./screenshots:/app/screenshots
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/dist:/usr/share/nginx/html
      - ./screenshots:/usr/share/nginx/html/screenshots
      - ./uploads:/usr/share/nginx/html/uploads
    depends_on:
      - ai-automation
    restart: unless-stopped
```

#### 3. 部署命令

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 监控和维护

### 1. 日志管理

```bash
# 查看PM2日志
pm2 logs

# 查看应用日志
tail -f logs/app.log

# 日志轮转配置
sudo logrotate -d /etc/logrotate.d/ai-automation
```

### 2. 性能监控

```bash
# PM2监控
pm2 monit

# 系统资源监控
htop
df -h
free -m
```

### 3. 数据备份

```bash
# 备份数据库
cp data/automation.db backups/automation_$(date +%Y%m%d_%H%M%S).db

# 备份截图
tar -czf backups/screenshots_$(date +%Y%m%d_%H%M%S).tar.gz screenshots/

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp data/automation.db $BACKUP_DIR/automation_$DATE.db

# 备份截图（保留最近7天）
tar -czf $BACKUP_DIR/screenshots_$DATE.tar.gz screenshots/
find $BACKUP_DIR -name "screenshots_*.tar.gz" -mtime +7 -delete

# 备份配置文件
cp .env $BACKUP_DIR/env_$DATE
```

### 4. 更新部署

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install
cd frontend && npm install && cd ..

# 重新构建
npm run build

# 重启服务
pm2 restart all

# 或使用Docker
docker-compose pull
docker-compose up -d --build
```

## 安全配置

### 1. 防火墙设置

```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 环境变量安全

- 不要在代码中硬编码敏感信息
- 使用强密码和API密钥
- 定期轮换API密钥
- 限制API访问权限

## 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   # 检查系统依赖
   npx playwright install --with-deps
   ```

2. **内存不足**
   ```bash
   # 增加swap空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

3. **端口占用**
   ```bash
   # 查找占用端口的进程
   sudo lsof -i :3001
   sudo kill -9 <PID>
   ```

4. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER /path/to/aitest
   chmod -R 755 /path/to/aitest
   ```

### 日志分析

查看关键日志文件：
- 应用日志: `logs/app.log`
- 错误日志: `logs/app-error.log`
- PM2日志: `~/.pm2/logs/`
- Nginx日志: `/var/log/nginx/`

## 性能优化

1. **启用gzip压缩**
2. **配置CDN**
3. **数据库优化**
4. **缓存策略**
5. **负载均衡**

详细的性能优化指南请参考 `docs/PERFORMANCE.md`。
