# MySQL数据库配置指南

## 📋 配置信息

根据您提供的信息，MySQL配置如下：

```
主机: 127.0.0.1
端口: 3306
用户名: root
密码: za05&vPx
数据库: aitest
```

## 🚀 快速设置

### 1. 确认MySQL服务运行

首先确保MySQL服务正在运行：

```bash
# macOS (使用Homebrew)
brew services start mysql

# Linux (Ubuntu/Debian)
sudo systemctl start mysql
sudo systemctl enable mysql

# Windows
# 通过服务管理器启动MySQL服务
```

### 2. 测试MySQL连接

使用命令行客户端测试连接：

```bash
mysql -h 127.0.0.1 -P 3306 -u root -p
# 输入密码: za05&vPx
```

### 3. 自动初始化数据库

运行我们提供的初始化脚本：

```bash
# 方法1: 使用Node.js脚本（推荐）
npm run setup-db

# 方法2: 直接使用SQL文件
mysql -u root -p < scripts/setup-database.sql
```

### 4. 验证数据库设置

```bash
# 测试数据库连接
npm run test-db
```

## 📁 数据库结构

初始化后将创建以下表：

### 主要表结构

1. **website_test_results** - 网站测试结果
   - 存储测试基本信息、状态、统计数据
   
2. **link_test_details** - 链接测试详情
   - 存储每个链接的测试结果、响应时间、错误信息
   
3. **error_logs** - 错误日志
   - 存储详细的错误信息、堆栈跟踪
   
4. **test_cases** - 测试用例（兼容原系统）
   - 存储自动化测试用例
   
5. **test_executions** - 测试执行记录（兼容原系统）
   - 存储测试执行历史

## 🔧 手动配置步骤

如果自动脚本失败，可以手动配置：

### 1. 创建数据库

```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE aitest CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE aitest;
```

### 2. 创建表结构

```sql
-- 网站测试结果表
CREATE TABLE website_test_results (
    id VARCHAR(36) PRIMARY KEY,
    website_url VARCHAR(2048) NOT NULL,
    test_type ENUM('link_check', 'error_monitor', 'full_test') NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed') NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NULL,
    total_links INT DEFAULT 0,
    successful_links INT DEFAULT 0,
    failed_links INT DEFAULT 0,
    server_errors INT DEFAULT 0,
    frontend_errors INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_website_url (website_url(255)),
    INDEX idx_status (status),
    INDEX idx_test_type (test_type),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 其他表结构请参考 scripts/setup-database.sql
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 连接被拒绝 (ECONNREFUSED)

```bash
# 检查MySQL是否运行
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# 启动MySQL服务
sudo systemctl start mysql  # Linux
brew services start mysql  # macOS
```

#### 2. 访问被拒绝 (ER_ACCESS_DENIED_ERROR)

```bash
# 检查用户名和密码
mysql -u root -p

# 如果忘记密码，重置root密码
sudo mysql
ALTER USER 'root'@'localhost' IDENTIFIED BY 'za05&vPx';
FLUSH PRIVILEGES;
```

#### 3. 数据库不存在 (ER_BAD_DB_ERROR)

```bash
# 运行初始化脚本
npm run setup-db

# 或手动创建
mysql -u root -p -e "CREATE DATABASE aitest CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

#### 4. 端口被占用

```bash
# 检查3306端口
netstat -tlnp | grep 3306
lsof -i :3306

# 如果MySQL运行在其他端口，更新.env文件中的MYSQL_PORT
```

## 📝 环境变量配置

确保 `.env` 文件包含正确的配置：

```env
# MySQL数据库配置
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=za05&vPx
MYSQL_DATABASE=aitest
MYSQL_CONNECTION_LIMIT=10
MYSQL_ACQUIRE_TIMEOUT=60000
MYSQL_TIMEOUT=60000
```

## 🧪 测试数据库连接

运行测试脚本验证配置：

```bash
# 完整的数据库测试
npm run test-db

# 如果测试通过，启动应用
npm run dev

# 访问网站测试页面
open http://localhost:3000/website-test
```

## 🔄 重置数据库

如果需要重置数据库：

```bash
# 删除所有表和数据
mysql -u root -p -e "DROP DATABASE IF EXISTS aitest;"

# 重新初始化
npm run setup-db
```

## 📊 监控和维护

### 查看数据库状态

```sql
-- 查看表大小
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'aitest'
ORDER BY (data_length + index_length) DESC;

-- 查看测试统计
SELECT 
    test_type,
    status,
    COUNT(*) as count
FROM website_test_results 
GROUP BY test_type, status;
```

### 备份数据库

```bash
# 备份数据库
mysqldump -u root -p aitest > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
mysql -u root -p aitest < backup_file.sql
```

## 🎯 下一步

数据库配置完成后：

1. ✅ 运行 `npm run test-db` 验证连接
2. ✅ 启动应用 `npm run dev`
3. ✅ 访问 http://localhost:3000/website-test
4. ✅ 点击"快速测试 webopte.com"开始测试

如有问题，请检查日志文件或联系技术支持。
