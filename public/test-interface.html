<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站测试界面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h2 {
            color: #262626;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input[type="url"], input[type="text"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        .quick-test {
            background-color: #52c41a;
        }
        .quick-test:hover {
            background-color: #73d13d;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
        }
        .error {
            background-color: #fff2f0;
            border-color: #ffccc7;
            color: #cf1322;
        }
        .loading {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            color: #0050b3;
        }
        .result-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #1890ff;
        }
        .status-success { border-left-color: #52c41a; }
        .status-error { border-left-color: #ff4d4f; }
        .status-warning { border-left-color: #faad14; }
        .history {
            max-height: 400px;
            overflow-y: auto;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #fafafa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 网站自动化测试平台</h1>
        
        <!-- 快速测试 -->
        <div class="test-section">
            <h2>🎯 快速测试 webopte.com</h2>
            <p>一键测试 webopte.com 网站的所有链接和错误检测</p>
            <button class="quick-test" onclick="testWebopte()">开始测试 webopte.com</button>
            <button onclick="getTestHistory()">查看测试历史</button>
        </div>

        <!-- 自定义测试 -->
        <div class="test-section">
            <h2>🔧 自定义网站测试</h2>
            <div class="form-group">
                <label for="websiteUrl">网站URL:</label>
                <input type="url" id="websiteUrl" placeholder="https://example.com" value="https://www.webopte.com/">
            </div>
            <button onclick="testCustomWebsite()">开始测试</button>
        </div>

        <!-- 测试结果 -->
        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="results"></div>
        </div>

        <!-- 测试历史 -->
        <div class="test-section">
            <h2>📈 测试历史</h2>
            <div id="history" class="history"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/website-test';
        
        function showLoading(message = '正在测试...') {
            document.getElementById('results').innerHTML = `
                <div class="results loading">
                    <strong>⏳ ${message}</strong>
                    <div>请稍候，这可能需要几分钟时间...</div>
                </div>
            `;
        }
        
        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="results error">
                    <strong>❌ 测试失败</strong>
                    <div>${message}</div>
                </div>
            `;
        }
        
        function showSuccess(data) {
            const result = data.data;
            const stats = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${result.totalLinks}</div>
                        <div class="stat-label">总链接数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.successfulLinks}</div>
                        <div class="stat-label">成功链接</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.failedLinks}</div>
                        <div class="stat-label">失败链接</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${result.serverErrors}</div>
                        <div class="stat-label">服务端错误</div>
                    </div>
                </div>
            `;
            
            let linksHtml = '';
            if (result.links && result.links.length > 0) {
                linksHtml = '<h4>链接测试详情:</h4>';
                result.links.forEach(link => {
                    const statusClass = link.success ? 'status-success' : 'status-error';
                    linksHtml += `
                        <div class="result-item ${statusClass}">
                            <strong>${link.success ? '✅' : '❌'} ${link.url}</strong>
                            <div>状态码: ${link.status} ${link.error ? '| 错误: ' + link.error : ''}</div>
                        </div>
                    `;
                });
            }
            
            document.getElementById('results').innerHTML = `
                <div class="results">
                    <strong>✅ 测试完成</strong>
                    <div>网站: ${result.websiteUrl}</div>
                    <div>测试时间: ${new Date(result.startTime).toLocaleString()}</div>
                    <div>响应时间: ${result.responseTime}ms</div>
                    ${stats}
                    ${linksHtml}
                </div>
            `;
        }
        
        async function testWebopte() {
            showLoading('正在测试 webopte.com...');
            
            try {
                const response = await fetch(`${API_BASE}/webopte`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess(data);
                    // 自动刷新历史
                    setTimeout(getTestHistory, 1000);
                } else {
                    showError(data.error || '测试失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        async function testCustomWebsite() {
            const url = document.getElementById('websiteUrl').value;
            if (!url) {
                showError('请输入网站URL');
                return;
            }
            
            showLoading(`正在测试 ${url}...`);
            
            try {
                const response = await fetch(`${API_BASE}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        websiteUrl: url
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showSuccess(data);
                    // 自动刷新历史
                    setTimeout(getTestHistory, 1000);
                } else {
                    showError(data.error || '测试失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        async function getTestHistory() {
            try {
                const response = await fetch(`${API_BASE}/history`);
                const data = await response.json();
                
                if (data.success && data.data.tests) {
                    let historyHtml = '';
                    data.data.tests.forEach(test => {
                        const statusClass = test.status === 'completed' ? 'status-success' : 
                                          test.status === 'failed' ? 'status-error' : 'status-warning';
                        const statusIcon = test.status === 'completed' ? '✅' : 
                                         test.status === 'failed' ? '❌' : '⏳';
                        
                        historyHtml += `
                            <div class="result-item ${statusClass}">
                                <strong>${statusIcon} ${test.websiteUrl}</strong>
                                <div>状态: ${test.status} | 时间: ${new Date(test.startTime).toLocaleString()}</div>
                                <div>链接: ${test.totalLinks} (成功: ${test.successfulLinks}, 失败: ${test.failedLinks})</div>
                                <div>错误: 服务端 ${test.serverErrors}, 前端 ${test.frontendErrors}</div>
                            </div>
                        `;
                    });
                    
                    document.getElementById('history').innerHTML = historyHtml || '<div>暂无测试历史</div>';
                } else {
                    document.getElementById('history').innerHTML = '<div>获取历史失败</div>';
                }
            } catch (error) {
                document.getElementById('history').innerHTML = '<div>网络错误: ' + error.message + '</div>';
            }
        }
        
        // 页面加载时获取测试历史
        window.onload = function() {
            getTestHistory();
        };
    </script>
</body>
</html>
