#!/usr/bin/env node

/**
 * MySQL数据库初始化脚本
 * 自动创建数据库和表结构
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../.env') });

// MySQL配置
const config = {
  host: process.env.MYSQL_HOST || '127.0.0.1',
  port: parseInt(process.env.MYSQL_PORT || '3306', 10),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'aitest',
  charset: 'utf8mb4'
};

async function initializeDatabase() {
  console.log('🚀 Initializing MySQL database...');
  console.log('📋 Configuration:');
  console.log(`   Host: ${config.host}`);
  console.log(`   Port: ${config.port}`);
  console.log(`   User: ${config.user}`);
  console.log(`   Database: ${config.database}`);
  console.log('');

  let connection;

  try {
    // 1. 连接到MySQL服务器（不指定数据库）
    console.log('1️⃣ Connecting to MySQL server...');
    const basicConfig = { ...config };
    delete basicConfig.database;
    
    connection = await mysql.createConnection(basicConfig);
    console.log('✅ Connected to MySQL server!');

    // 2. 创建数据库
    console.log('2️⃣ Creating database...');
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ Database '${config.database}' created!`);

    // 3. 重新连接到目标数据库
    await connection.end();
    connection = await mysql.createConnection(config);
    console.log(`✅ Connected to database '${config.database}'`);

    // 4. 创建表结构
    console.log('3️⃣ Creating tables...');

    // 网站测试结果表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS website_test_results (
        id VARCHAR(36) PRIMARY KEY,
        website_url VARCHAR(2048) NOT NULL,
        test_type ENUM('link_check', 'error_monitor', 'full_test') NOT NULL,
        status ENUM('pending', 'running', 'completed', 'failed') NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NULL,
        total_links INT DEFAULT 0,
        successful_links INT DEFAULT 0,
        failed_links INT DEFAULT 0,
        server_errors INT DEFAULT 0,
        frontend_errors INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_website_url (website_url(255)),
        INDEX idx_status (status),
        INDEX idx_test_type (test_type),
        INDEX idx_start_time (start_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created table: website_test_results');

    // 链接测试详情表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS link_test_details (
        id VARCHAR(36) PRIMARY KEY,
        test_result_id VARCHAR(36) NOT NULL,
        url VARCHAR(2048) NOT NULL,
        http_status INT NULL,
        response_time INT NULL,
        error_message TEXT NULL,
        server_error BOOLEAN DEFAULT FALSE,
        frontend_error BOOLEAN DEFAULT FALSE,
        screenshot_path VARCHAR(512) NULL,
        tested_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (test_result_id) REFERENCES website_test_results(id) ON DELETE CASCADE,
        INDEX idx_test_result_id (test_result_id),
        INDEX idx_url (url(255)),
        INDEX idx_http_status (http_status),
        INDEX idx_tested_at (tested_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created table: link_test_details');

    // 错误日志表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS error_logs (
        id VARCHAR(36) PRIMARY KEY,
        test_result_id VARCHAR(36) NULL,
        link_detail_id VARCHAR(36) NULL,
        error_type ENUM('server_error', 'frontend_error', 'network_error', 'timeout_error') NOT NULL,
        error_code VARCHAR(50) NULL,
        error_message TEXT NOT NULL,
        stack_trace TEXT NULL,
        url VARCHAR(2048) NULL,
        user_agent VARCHAR(512) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (test_result_id) REFERENCES website_test_results(id) ON DELETE SET NULL,
        FOREIGN KEY (link_detail_id) REFERENCES link_test_details(id) ON DELETE SET NULL,
        INDEX idx_test_result_id (test_result_id),
        INDEX idx_error_type (error_type),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created table: error_logs');

    // 兼容原有系统的表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS test_cases (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        steps TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        tags TEXT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created table: test_cases');

    await connection.execute(`
      CREATE TABLE IF NOT EXISTS test_executions (
        id VARCHAR(36) PRIMARY KEY,
        test_case_id VARCHAR(36),
        status VARCHAR(50) NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME,
        results TEXT,
        screenshots TEXT,
        logs TEXT,
        error TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_executions_status (status),
        INDEX idx_executions_start_time (start_time),
        INDEX idx_executions_test_case_id (test_case_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created table: test_executions');

    // 5. 验证表创建
    console.log('4️⃣ Verifying tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`✅ Created ${tables.length} tables:`, tables.map(t => Object.values(t)[0]));

    // 6. 插入示例数据（可选）
    console.log('5️⃣ Inserting sample data...');
    const sampleId = 'sample-webopte-test-001';
    await connection.execute(`
      INSERT IGNORE INTO website_test_results (
        id, website_url, test_type, status, start_time,
        total_links, successful_links, failed_links, server_errors, frontend_errors
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?)
    `, [sampleId, 'https://www.webopte.com/', 'full_test', 'completed', 25, 23, 2, 1, 1]);
    console.log('✅ Sample data inserted');

    await connection.end();
    console.log('');
    console.log('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('📝 Next steps:');
    console.log('   1. Test the connection: npm run test-db');
    console.log('   2. Start the application: npm run dev');
    console.log('   3. Visit: http://localhost:3000/website-test');

  } catch (error) {
    console.error('❌ Database initialization failed!');
    console.error('');
    console.error('🔍 Error details:');
    console.error(`   Code: ${error.code}`);
    console.error(`   Message: ${error.message}`);
    console.error('');

    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure MySQL server is running on port 3306');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Check your MySQL username and password');
    }

    if (connection) {
      try {
        await connection.end();
      } catch (closeError) {
        // 忽略关闭连接时的错误
      }
    }

    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
