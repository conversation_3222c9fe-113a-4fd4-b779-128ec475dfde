#!/usr/bin/env node

/**
 * MySQL数据库连接测试脚本
 * 用于验证数据库配置是否正确
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../.env') });

// MySQL配置
const config = {
  host: process.env.MYSQL_HOST || '127.0.0.1',
  port: parseInt(process.env.MYSQL_PORT || '3306', 10),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'aitest',
  charset: 'utf8mb4'
};

async function testDatabaseConnection() {
  console.log('🔍 Testing MySQL database connection...');
  console.log('📋 Configuration:');
  console.log(`   Host: ${config.host}`);
  console.log(`   Port: ${config.port}`);
  console.log(`   User: ${config.user}`);
  console.log(`   Database: ${config.database}`);
  console.log('');

  let connection;

  try {
    // 1. 测试基本连接（不指定数据库）
    console.log('1️⃣ Testing basic MySQL connection...');
    const basicConfig = { ...config };
    delete basicConfig.database;
    
    connection = await mysql.createConnection(basicConfig);
    console.log('✅ Basic MySQL connection successful!');
    await connection.end();

    // 2. 创建数据库（如果不存在）
    console.log('2️⃣ Creating database if not exists...');
    connection = await mysql.createConnection(basicConfig);
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ Database '${config.database}' is ready!`);
    await connection.end();

    // 3. 测试数据库连接
    console.log('3️⃣ Testing database connection...');
    connection = await mysql.createConnection(config);
    console.log('✅ Database connection successful!');

    // 4. 测试查询
    console.log('4️⃣ Testing database query...');
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ Database query successful!', rows);

    // 5. 检查表是否存在
    console.log('5️⃣ Checking tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 Found ${tables.length} tables:`, tables.map(t => Object.values(t)[0]));

    // 6. 如果没有表，提示需要运行初始化脚本
    if (tables.length === 0) {
      console.log('');
      console.log('⚠️  No tables found. You need to run the database initialization script:');
      console.log('   mysql -u root -p < scripts/setup-database.sql');
      console.log('   Or run: npm run setup-db');
    } else {
      console.log('✅ Database tables are ready!');
    }

    await connection.end();
    console.log('');
    console.log('🎉 All database tests passed!');
    console.log('');
    console.log('📝 Next steps:');
    console.log('   1. If tables are missing, run: npm run setup-db');
    console.log('   2. Start the application: npm run dev');
    console.log('   3. Visit: http://localhost:3000/website-test');

  } catch (error) {
    console.error('❌ Database connection failed!');
    console.error('');
    console.error('🔍 Error details:');
    console.error(`   Code: ${error.code}`);
    console.error(`   Message: ${error.message}`);
    console.error('');
    
    // 提供常见错误的解决方案
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Possible solutions:');
      console.error('   1. Make sure MySQL server is running');
      console.error('   2. Check if MySQL is listening on port 3306');
      console.error('   3. Verify the host and port in .env file');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Possible solutions:');
      console.error('   1. Check username and password in .env file');
      console.error('   2. Make sure the user has proper permissions');
      console.error('   3. Try connecting with mysql client: mysql -u root -p');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 Possible solutions:');
      console.error('   1. The database will be created automatically');
      console.error('   2. Or create manually: CREATE DATABASE aitest;');
    }
    
    console.error('');
    console.error('🔧 Current configuration:');
    console.error(`   MYSQL_HOST=${config.host}`);
    console.error(`   MYSQL_PORT=${config.port}`);
    console.error(`   MYSQL_USER=${config.user}`);
    console.error(`   MYSQL_PASSWORD=${config.password ? '[SET]' : '[NOT SET]'}`);
    console.error(`   MYSQL_DATABASE=${config.database}`);

    if (connection) {
      try {
        await connection.end();
      } catch (closeError) {
        // 忽略关闭连接时的错误
      }
    }

    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testDatabaseConnection();
}

module.exports = { testDatabaseConnection };
