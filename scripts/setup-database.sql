-- MySQL数据库初始化脚本
-- 为AI浏览器自动化测试平台创建数据库和表结构

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS aitest 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE aitest;

-- 创建网站测试结果表
CREATE TABLE IF NOT EXISTS website_test_results (
    id VARCHAR(36) PRIMARY KEY,
    website_url VARCHAR(2048) NOT NULL,
    test_type ENUM('link_check', 'error_monitor', 'full_test') NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed') NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NULL,
    total_links INT DEFAULT 0,
    successful_links INT DEFAULT 0,
    failed_links INT DEFAULT 0,
    server_errors INT DEFAULT 0,
    frontend_errors INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_website_url (website_url(255)),
    INDEX idx_status (status),
    INDEX idx_test_type (test_type),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建链接测试详情表
CREATE TABLE IF NOT EXISTS link_test_details (
    id VARCHAR(36) PRIMARY KEY,
    test_result_id VARCHAR(36) NOT NULL,
    url VARCHAR(2048) NOT NULL,
    http_status INT NULL,
    response_time INT NULL,
    error_message TEXT NULL,
    server_error BOOLEAN DEFAULT FALSE,
    frontend_error BOOLEAN DEFAULT FALSE,
    screenshot_path VARCHAR(512) NULL,
    tested_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (test_result_id) REFERENCES website_test_results(id) ON DELETE CASCADE,
    INDEX idx_test_result_id (test_result_id),
    INDEX idx_url (url(255)),
    INDEX idx_http_status (http_status),
    INDEX idx_tested_at (tested_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建错误日志表
CREATE TABLE IF NOT EXISTS error_logs (
    id VARCHAR(36) PRIMARY KEY,
    test_result_id VARCHAR(36) NULL,
    link_detail_id VARCHAR(36) NULL,
    error_type ENUM('server_error', 'frontend_error', 'network_error', 'timeout_error') NOT NULL,
    error_code VARCHAR(50) NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT NULL,
    url VARCHAR(2048) NULL,
    user_agent VARCHAR(512) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (test_result_id) REFERENCES website_test_results(id) ON DELETE SET NULL,
    FOREIGN KEY (link_detail_id) REFERENCES link_test_details(id) ON DELETE SET NULL,
    INDEX idx_test_result_id (test_result_id),
    INDEX idx_error_type (error_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建测试用例表（兼容原有系统）
CREATE TABLE IF NOT EXISTS test_cases (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    steps TEXT NOT NULL, -- JSON格式存储步骤
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    tags TEXT -- JSON格式存储标签
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建测试执行表（兼容原有系统）
CREATE TABLE IF NOT EXISTS test_executions (
    id VARCHAR(36) PRIMARY KEY,
    test_case_id VARCHAR(36),
    status VARCHAR(50) NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    results TEXT, -- JSON格式存储结果
    screenshots TEXT, -- JSON格式存储截图路径
    logs TEXT, -- JSON格式存储日志
    error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_executions_status (status),
    INDEX idx_executions_start_time (start_time),
    INDEX idx_executions_test_case_id (test_case_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据（可选）
INSERT IGNORE INTO website_test_results (
    id, 
    website_url, 
    test_type, 
    status, 
    start_time,
    total_links,
    successful_links,
    failed_links,
    server_errors,
    frontend_errors
) VALUES (
    'example-test-001',
    'https://www.webopte.com/',
    'full_test',
    'completed',
    NOW(),
    25,
    23,
    2,
    1,
    1
);

-- 显示创建的表
SHOW TABLES;

-- 显示表结构
DESCRIBE website_test_results;
DESCRIBE link_test_details;
DESCRIBE error_logs;
