import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { SimpleAutomationEngine } from '../services/SimpleAutomationEngine';
import { StepParser } from '../services/StepParser';
import { MemoryDatabase } from '../services/MemoryDatabase';
import { 
  AutomationRequest, 
  AutomationResponse, 
  ApiResponse,
  TestExecution 
} from '@shared/types';
import { Logger } from '../utils/logger';

export class AutomationController {
  private logger: Logger;
  private stepParser: StepParser;
  private databaseService: MemoryDatabase;
  private activeExecutions: Map<string, SimpleAutomationEngine> = new Map();

  constructor() {
    this.logger = new Logger('AutomationController');
    this.stepParser = new StepParser();
    this.databaseService = new MemoryDatabase();
  }

  /**
   * 执行自动化测试
   */
  async executeTest(req: Request, res: Response): Promise<void> {
    try {
      const requestData: AutomationRequest = req.body;
      const executionId = uuidv4();
      
      this.logger.info('Starting test execution', { 
        executionId, 
        stepsCount: requestData.steps.length 
      });

      // 创建自动化引擎实例
      const automationEngine = new SimpleAutomationEngine();
      this.activeExecutions.set(executionId, automationEngine);

      // 立即返回执行ID
      const response: AutomationResponse = {
        executionId,
        status: 'started',
        message: 'Test execution started successfully',
      };

      res.json({
        success: true,
        data: response,
        message: 'Test execution started',
      } as ApiResponse<AutomationResponse>);

      // 异步执行测试
      this.executeTestAsync(executionId, requestData, automationEngine);

    } catch (error) {
      this.logger.error('Failed to start test execution', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to start test execution',
      } as ApiResponse);
    }
  }

  /**
   * 异步执行测试
   */
  private async executeTestAsync(
    executionId: string,
    requestData: AutomationRequest,
    automationEngine: SimpleAutomationEngine
  ): Promise<void> {
    try {
      // 初始化自动化引擎
      await automationEngine.initialize(
        requestData.config?.aiProvider,
        requestData.config?.browserConfig
      );

      // 执行测试步骤
      const execution = await automationEngine.executeSteps(
        requestData.steps,
        requestData.testCaseId,
        {
          saveScreenshots: requestData.config?.saveScreenshots !== false,
          generateReport: requestData.config?.generateReport !== false,
        }
      );

      // 保存执行结果到数据库
      await this.databaseService.saveExecution(execution);

      this.logger.info('Test execution completed', { 
        executionId, 
        status: execution.status 
      });

    } catch (error) {
      this.logger.error('Test execution failed', error);
      
      // 保存失败的执行记录
      const failedExecution: TestExecution = {
        id: executionId,
        testCaseId: requestData.testCaseId || '',
        status: 'failed',
        startTime: new Date(),
        endTime: new Date(),
        results: [],
        screenshots: [],
        logs: [],
        error: error instanceof Error ? error.message : String(error),
      };

      await this.databaseService.saveExecution(failedExecution);

    } finally {
      // 清理资源
      await automationEngine.cleanup();
      this.activeExecutions.delete(executionId);
    }
  }

  /**
   * 解析自然语言步骤
   */
  async parseSteps(req: Request, res: Response): Promise<void> {
    try {
      const { steps } = req.body;
      
      this.logger.info('Parsing steps', { stepsCount: steps.length });

      const parsedSteps = this.stepParser.parseSteps(steps);
      
      // 验证步骤
      const validationResults = parsedSteps.map(step => ({
        step,
        validation: this.stepParser.validateStep(step),
      }));

      res.json({
        success: true,
        data: {
          parsedSteps,
          validationResults,
        },
        message: 'Steps parsed successfully',
      } as ApiResponse);

    } catch (error) {
      this.logger.error('Failed to parse steps', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to parse steps',
      } as ApiResponse);
    }
  }

  /**
   * 获取执行状态
   */
  async getExecutionStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // 首先检查是否是活跃的执行
      const activeEngine = this.activeExecutions.get(id);
      if (activeEngine) {
        const currentExecution = activeEngine.getCurrentExecution();
        if (currentExecution) {
          res.json({
            success: true,
            data: currentExecution,
            message: 'Execution status retrieved',
          } as ApiResponse<TestExecution>);
          return;
        }
      }

      // 从数据库查询历史执行记录
      const execution = await this.databaseService.getExecution(id);
      if (execution) {
        res.json({
          success: true,
          data: execution,
          message: 'Execution status retrieved',
        } as ApiResponse<TestExecution>);
      } else {
        res.status(404).json({
          success: false,
          error: 'Execution not found',
          message: 'Execution not found',
        } as ApiResponse);
      }

    } catch (error) {
      this.logger.error('Failed to get execution status', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get execution status',
      } as ApiResponse);
    }
  }

  /**
   * 取消执行
   */
  async cancelExecution(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const activeEngine = this.activeExecutions.get(id);
      if (activeEngine) {
        await activeEngine.cleanup();
        this.activeExecutions.delete(id);
        
        this.logger.info('Execution cancelled', { executionId: id });
        
        res.json({
          success: true,
          message: 'Execution cancelled successfully',
        } as ApiResponse);
      } else {
        res.status(404).json({
          success: false,
          error: 'Active execution not found',
          message: 'Execution not found or already completed',
        } as ApiResponse);
      }

    } catch (error) {
      this.logger.error('Failed to cancel execution', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to cancel execution',
      } as ApiResponse);
    }
  }

  /**
   * 获取执行历史
   */
  async getExecutionHistory(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const status = req.query.status as string;

      const executions = await this.databaseService.getExecutions({
        page,
        limit,
        status,
      });

      res.json({
        success: true,
        data: executions,
        message: 'Execution history retrieved',
      } as ApiResponse);

    } catch (error) {
      this.logger.error('Failed to get execution history', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get execution history',
      } as ApiResponse);
    }
  }

  /**
   * 获取测试报告
   */
  async getTestReport(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const execution = await this.databaseService.getExecution(id);
      if (!execution) {
        res.status(404).json({
          success: false,
          error: 'Execution not found',
          message: 'Execution not found',
        } as ApiResponse);
        return;
      }

      // 生成报告（如果还没有生成）
      // 这里可以实现报告生成逻辑
      
      res.json({
        success: true,
        data: {
          executionId: id,
          reportUrl: `/api/automation/execution/${id}/report/download`,
          execution,
        },
        message: 'Test report retrieved',
      } as ApiResponse);

    } catch (error) {
      this.logger.error('Failed to get test report', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get test report',
      } as ApiResponse);
    }
  }

  /**
   * 获取执行截图列表
   */
  async getExecutionScreenshots(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const execution = await this.databaseService.getExecution(id);
      if (!execution) {
        res.status(404).json({
          success: false,
          error: 'Execution not found',
          message: 'Execution not found',
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: {
          executionId: id,
          screenshots: execution.screenshots,
        },
        message: 'Screenshots retrieved',
      } as ApiResponse);

    } catch (error) {
      this.logger.error('Failed to get execution screenshots', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get execution screenshots',
      } as ApiResponse);
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        activeExecutions: this.activeExecutions.size,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
      };

      res.json({
        success: true,
        data: health,
        message: 'Service is healthy',
      } as ApiResponse);

    } catch (error) {
      this.logger.error('Health check failed', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Health check failed',
      } as ApiResponse);
    }
  }
}
