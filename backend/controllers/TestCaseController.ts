import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { MemoryDatabase } from '../services/MemoryDatabase';
import { SimpleAutomationEngine } from '../services/SimpleAutomationEngine';
import { TestCase, TestStep, ApiResponse } from '@shared/types';
import { Logger } from '../utils/logger';

export class TestCaseController {
  private logger: Logger;
  private databaseService: MemoryDatabase;

  constructor() {
    this.logger = new Logger('TestCaseController');
    this.databaseService = new MemoryDatabase();
  }

  /**
   * 创建测试用例
   */
  async createTestCase(req: Request, res: Response): Promise<void> {
    try {
      const { name, description, steps, tags } = req.body;
      
      const testCase: TestCase = {
        id: uuidv4(),
        name,
        description: description || '',
        steps: steps.map((step: any, index: number) => ({
          id: uuidv4(),
          description: step.description,
          action: step.action,
          target: step.target,
          value: step.value,
          expectedResult: step.expectedResult,
          order: index,
        })),
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: tags || [],
      };

      await this.databaseService.saveTestCase(testCase);

      this.logger.info('Test case created successfully', { 
        testCaseId: testCase.id,
        name: testCase.name 
      });

      res.status(201).json({
        success: true,
        data: testCase,
        message: 'Test case created successfully',
      } as ApiResponse<TestCase>);

    } catch (error) {
      this.logger.error('Failed to create test case', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to create test case',
      } as ApiResponse);
    }
  }

  /**
   * 获取所有测试用例
   */
  async getTestCases(req: Request, res: Response): Promise<void> {
    try {
      const testCases = await this.databaseService.getTestCases();

      res.json({
        success: true,
        data: testCases,
        message: 'Test cases retrieved successfully',
      } as ApiResponse<TestCase[]>);

    } catch (error) {
      this.logger.error('Failed to get test cases', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get test cases',
      } as ApiResponse);
    }
  }

  /**
   * 获取单个测试用例
   */
  async getTestCase(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const testCase = await this.databaseService.getTestCase(id);
      
      if (!testCase) {
        res.status(404).json({
          success: false,
          error: 'Test case not found',
          message: 'Test case not found',
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: testCase,
        message: 'Test case retrieved successfully',
      } as ApiResponse<TestCase>);

    } catch (error) {
      this.logger.error('Failed to get test case', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to get test case',
      } as ApiResponse);
    }
  }

  /**
   * 更新测试用例
   */
  async updateTestCase(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updates = req.body;

      const existingTestCase = await this.databaseService.getTestCase(id);
      
      if (!existingTestCase) {
        res.status(404).json({
          success: false,
          error: 'Test case not found',
          message: 'Test case not found',
        } as ApiResponse);
        return;
      }

      const updatedTestCase: TestCase = {
        ...existingTestCase,
        ...updates,
        id, // 确保ID不被修改
        updatedAt: new Date(),
      };

      // 如果更新了步骤，重新生成步骤ID和顺序
      if (updates.steps) {
        updatedTestCase.steps = updates.steps.map((step: any, index: number) => ({
          id: step.id || uuidv4(),
          description: step.description,
          action: step.action,
          target: step.target,
          value: step.value,
          expectedResult: step.expectedResult,
          order: index,
        }));
      }

      await this.databaseService.saveTestCase(updatedTestCase);

      this.logger.info('Test case updated successfully', { 
        testCaseId: id,
        name: updatedTestCase.name 
      });

      res.json({
        success: true,
        data: updatedTestCase,
        message: 'Test case updated successfully',
      } as ApiResponse<TestCase>);

    } catch (error) {
      this.logger.error('Failed to update test case', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to update test case',
      } as ApiResponse);
    }
  }

  /**
   * 删除测试用例
   */
  async deleteTestCase(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const existingTestCase = await this.databaseService.getTestCase(id);
      
      if (!existingTestCase) {
        res.status(404).json({
          success: false,
          error: 'Test case not found',
          message: 'Test case not found',
        } as ApiResponse);
        return;
      }

      await this.databaseService.deleteTestCase(id);

      this.logger.info('Test case deleted successfully', { 
        testCaseId: id,
        name: existingTestCase.name 
      });

      res.json({
        success: true,
        message: 'Test case deleted successfully',
      } as ApiResponse);

    } catch (error) {
      this.logger.error('Failed to delete test case', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to delete test case',
      } as ApiResponse);
    }
  }

  /**
   * 执行测试用例
   */
  async executeTestCase(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { config } = req.body;

      const testCase = await this.databaseService.getTestCase(id);
      
      if (!testCase) {
        res.status(404).json({
          success: false,
          error: 'Test case not found',
          message: 'Test case not found',
        } as ApiResponse);
        return;
      }

      // 提取步骤描述
      const stepDescriptions = testCase.steps
        .sort((a, b) => a.order - b.order)
        .map(step => step.description);

      // 创建自动化引擎实例并执行
      const automationEngine = new SimpleAutomationEngine();
      
      try {
        await automationEngine.initialize(
          config?.aiProvider,
          config?.browserConfig
        );

        const execution = await automationEngine.executeSteps(
          stepDescriptions,
          testCase.id,
          {
            saveScreenshots: config?.saveScreenshots !== false,
            generateReport: config?.generateReport !== false,
          }
        );

        // 保存执行结果
        await this.databaseService.saveExecution(execution);

        this.logger.info('Test case executed successfully', { 
          testCaseId: id,
          executionId: execution.id 
        });

        res.json({
          success: true,
          data: {
            executionId: execution.id,
            status: execution.status,
            testCase,
          },
          message: 'Test case execution started',
        } as ApiResponse);

      } finally {
        await automationEngine.cleanup();
      }

    } catch (error) {
      this.logger.error('Failed to execute test case', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to execute test case',
      } as ApiResponse);
    }
  }
}
