import { Request, Response } from 'express';
import { z } from 'zod';
import { Logger } from '../utils/logger';
import { WebsiteTestService } from '../services/WebsiteTestService';
import { MySQLDatabaseService } from '../services/MySQLDatabaseService';
import { 
  WebsiteTestConfig, 
  WebsiteTestRequest, 
  WebsiteTestResponse,
  TestHistoryQuery,
  ApiResponse 
} from '@shared/types/websiteTest';

// 请求验证模式
const websiteTestConfigSchema = z.object({
  websiteUrl: z.string().url('Invalid website URL'),
  testType: z.enum(['link_check', 'error_monitor', 'full_test']),
  browserConfig: z.object({
    headless: z.boolean().optional(),
    viewport: z.object({
      width: z.number().optional(),
      height: z.number().optional()
    }).optional(),
    timeout: z.number().optional(),
    userAgent: z.string().optional()
  }).optional(),
  crawlerConfig: z.object({
    maxDepth: z.number().min(1).max(10).optional(),
    maxPages: z.number().min(1).max(1000).optional(),
    followExternalLinks: z.boolean().optional(),
    respectRobotsTxt: z.boolean().optional(),
    delay: z.number().min(100).optional(),
    concurrent: z.number().min(1).max(10).optional()
  }).optional(),
  errorMonitorConfig: z.object({
    captureScreenshots: z.boolean().optional(),
    captureConsoleLogs: z.boolean().optional(),
    captureNetworkErrors: z.boolean().optional(),
    monitorJavaScriptErrors: z.boolean().optional()
  }).optional()
});

const testHistoryQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).optional(),
  websiteUrl: z.string().optional(),
  testType: z.enum(['link_check', 'error_monitor', 'full_test']).optional(),
  status: z.enum(['pending', 'running', 'completed', 'failed']).optional(),
  startDate: z.string().transform(val => new Date(val)).optional(),
  endDate: z.string().transform(val => new Date(val)).optional(),
  sortBy: z.enum(['createdAt', 'startTime', 'endTime', 'duration']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

export class WebsiteTestController {
  private logger: Logger;
  private dbService: MySQLDatabaseService;
  private testService: WebsiteTestService;

  constructor() {
    this.logger = new Logger('WebsiteTestController');
    this.dbService = new MySQLDatabaseService();
    this.testService = new WebsiteTestService(this.dbService);
  }

  /**
   * 初始化控制器
   */
  async initialize(): Promise<void> {
    try {
      await this.dbService.initialize();
      await this.testService.initialize();
      this.logger.info('Website test controller initialized');
    } catch (error) {
      this.logger.error('Failed to initialize website test controller', error);
      throw error;
    }
  }

  /**
   * 执行网站测试
   */
  executeTest = async (req: Request, res: Response): Promise<void> => {
    try {
      this.logger.info('Received website test request', { 
        body: req.body,
        ip: req.ip 
      });

      // 验证请求数据
      const validationResult = websiteTestConfigSchema.safeParse(req.body);
      if (!validationResult.success) {
        const response: ApiResponse = {
          success: false,
          error: `Validation error: ${validationResult.error.issues.map(i => i.message).join(', ')}`,
          timestamp: new Date()
        };
        res.status(400).json(response);
        return;
      }

      const config: WebsiteTestConfig = validationResult.data;

      // 执行测试
      const testResult = await this.testService.executeTest(config);

      const response: WebsiteTestResponse = {
        success: true,
        data: {
          testResultId: testResult.id,
          status: testResult.status,
          message: 'Website test started successfully'
        }
      };

      this.logger.info('Website test started', { 
        testResultId: testResult.id,
        websiteUrl: config.websiteUrl 
      });

      res.status(200).json(response);
    } catch (error) {
      this.logger.error('Failed to execute website test', error);
      
      const response: ApiResponse = {
        success: false,
        error: (error as Error).message || 'Internal server error',
        timestamp: new Date()
      };
      
      res.status(500).json(response);
    }
  };

  /**
   * 获取测试结果
   */
  getTestResult = async (req: Request, res: Response): Promise<void> => {
    try {
      const { testResultId } = req.params;

      if (!testResultId) {
        const response: ApiResponse = {
          success: false,
          error: 'Test result ID is required',
          timestamp: new Date()
        };
        res.status(400).json(response);
        return;
      }

      const testResult = await this.testService.getTestResult(testResultId);

      if (!testResult) {
        const response: ApiResponse = {
          success: false,
          error: 'Test result not found',
          timestamp: new Date()
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: testResult,
        timestamp: new Date()
      };

      res.status(200).json(response);
    } catch (error) {
      this.logger.error('Failed to get test result', error);
      
      const response: ApiResponse = {
        success: false,
        error: (error as Error).message || 'Internal server error',
        timestamp: new Date()
      };
      
      res.status(500).json(response);
    }
  };

  /**
   * 获取测试历史
   */
  getTestHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      // 验证查询参数
      const validationResult = testHistoryQuerySchema.safeParse(req.query);
      if (!validationResult.success) {
        const response: ApiResponse = {
          success: false,
          error: `Query validation error: ${validationResult.error.issues.map(i => i.message).join(', ')}`,
          timestamp: new Date()
        };
        res.status(400).json(response);
        return;
      }

      const query: TestHistoryQuery = validationResult.data;

      // 设置默认值
      const options = {
        page: query.page || 1,
        limit: Math.min(query.limit || 20, 100), // 限制最大返回数量
        websiteUrl: query.websiteUrl,
        testType: query.testType,
        status: query.status
      };

      const testHistory = await this.testService.getTestHistory(options);

      const response: ApiResponse = {
        success: true,
        data: {
          tests: testHistory,
          pagination: {
            page: options.page,
            limit: options.limit,
            total: testHistory.length
          }
        },
        timestamp: new Date()
      };

      res.status(200).json(response);
    } catch (error) {
      this.logger.error('Failed to get test history', error);
      
      const response: ApiResponse = {
        success: false,
        error: (error as Error).message || 'Internal server error',
        timestamp: new Date()
      };
      
      res.status(500).json(response);
    }
  };

  /**
   * 停止测试
   */
  stopTest = async (req: Request, res: Response): Promise<void> => {
    try {
      const { testResultId } = req.params;

      if (!testResultId) {
        const response: ApiResponse = {
          success: false,
          error: 'Test result ID is required',
          timestamp: new Date()
        };
        res.status(400).json(response);
        return;
      }

      await this.testService.stopTest(testResultId);

      const response: ApiResponse = {
        success: true,
        data: { message: 'Test stopped successfully' },
        timestamp: new Date()
      };

      this.logger.info('Test stopped', { testResultId });
      res.status(200).json(response);
    } catch (error) {
      this.logger.error('Failed to stop test', error);
      
      const response: ApiResponse = {
        success: false,
        error: (error as Error).message || 'Internal server error',
        timestamp: new Date()
      };
      
      res.status(500).json(response);
    }
  };

  /**
   * 获取测试统计信息
   */
  getTestStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      // 这里可以实现统计信息的获取逻辑
      // 暂时返回基础统计信息
      const allTests = await this.testService.getTestHistory({ limit: 1000 });
      
      const statistics = {
        totalTests: allTests.length,
        completedTests: allTests.filter(t => t.status === 'completed').length,
        failedTests: allTests.filter(t => t.status === 'failed').length,
        runningTests: allTests.filter(t => t.status === 'running').length,
        averageDuration: 0, // 需要计算
        totalLinksChecked: allTests.reduce((sum, t) => sum + t.totalLinks, 0),
        totalErrorsFound: allTests.reduce((sum, t) => sum + t.serverErrors + t.frontendErrors, 0)
      };

      const response: ApiResponse = {
        success: true,
        data: statistics,
        timestamp: new Date()
      };

      res.status(200).json(response);
    } catch (error) {
      this.logger.error('Failed to get test statistics', error);
      
      const response: ApiResponse = {
        success: false,
        error: (error as Error).message || 'Internal server error',
        timestamp: new Date()
      };
      
      res.status(500).json(response);
    }
  };

  /**
   * 健康检查
   */
  healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      // 检查数据库连接
      const dbHealthy = true; // 这里可以添加实际的数据库健康检查

      const response: ApiResponse = {
        success: true,
        data: {
          status: 'healthy',
          database: dbHealthy ? 'connected' : 'disconnected',
          timestamp: new Date(),
          uptime: process.uptime()
        },
        timestamp: new Date()
      };

      res.status(200).json(response);
    } catch (error) {
      this.logger.error('Health check failed', error);
      
      const response: ApiResponse = {
        success: false,
        error: 'Service unhealthy',
        timestamp: new Date()
      };
      
      res.status(503).json(response);
    }
  };

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.testService.cleanup();
    await this.dbService.close();
    this.logger.info('Website test controller cleaned up');
  }
}
