import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';
import { config, validateConfig } from './config';
import { Logger } from './utils/logger';
import { initializeDirectories } from './utils/fileSystem';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import automationRoutes from './routes/automation';
import testCaseRoutes from './routes/testCases';
import websiteTestRoutes from './routes/websiteTest';

// 导入路由
const app = express();
const logger = new Logger('Server');

/**
 * 初始化应用程序
 */
async function initializeApp(): Promise<void> {
  try {
    // 验证配置
    validateConfig();
    logger.info('Configuration validated successfully');

    // 初始化目录结构
    await initializeDirectories();
    logger.info('Directory structure initialized');

    // 安全中间件
    app.use(helmet({
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          "default-src": ["'self'"],
          "style-src": ["'self'", "'unsafe-inline'"],
          "script-src": ["'self'"],
          "img-src": ["'self'", "data:", "https:"],
          "connect-src": ["'self'"],
          "font-src": ["'self'"],
          "object-src": ["'none'"],
          "media-src": ["'self'"],
          "frame-src": ["'none'"],
        },
      },
    }));

    // CORS配置
    app.use(cors({
      origin: config.server.corsOrigin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    }));

    // 请求解析中间件
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 静态文件服务
    app.use('/screenshots', express.static(config.storage.screenshotDir));
    app.use('/uploads', express.static(config.storage.uploadDir));

    // 请求日志中间件
    app.use((req, res, next) => {
      logger.info(`${req.method} ${req.url}`, {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });
      next();
    });

    // API路由
    app.use('/api/automation', automationRoutes);
    app.use('/api/test-cases', testCaseRoutes);
    app.use('/api/website-test', websiteTestRoutes);

    // 健康检查端点
    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API文档端点
    app.get('/api', (req, res) => {
      res.json({
        name: 'AI Browser Automation Agent API',
        version: '1.0.0',
        description: '基于浏览器的自动化测试AI智能体API',
        endpoints: {
          automation: {
            execute: 'POST /api/automation/execute',
            parse: 'POST /api/automation/parse',
            status: 'GET /api/automation/execution/:id',
            cancel: 'POST /api/automation/execution/:id/cancel',
            history: 'GET /api/automation/executions',
            report: 'GET /api/automation/execution/:id/report',
            screenshots: 'GET /api/automation/execution/:id/screenshots',
            health: 'GET /api/automation/health',
          },
        },
        documentation: 'https://github.com/your-repo/aitest#api-documentation',
      });
    });

    // 404处理
    app.use(notFoundHandler);

    // 全局错误处理
    app.use(errorHandler);

    logger.info('Application initialized successfully');

  } catch (error) {
    logger.error('Failed to initialize application', error);
    throw error;
  }
}

/**
 * 启动服务器
 */
async function startServer(): Promise<void> {
  try {
    await initializeApp();

    const server = app.listen(config.server.port, () => {
      logger.info(`Server started successfully`, {
        port: config.server.port,
        environment: process.env.NODE_ENV || 'development',
        corsOrigin: config.server.corsOrigin,
      });
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);
      
      server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('Forced shutdown due to timeout');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
}

// 启动服务器
if (require.main === module) {
  startServer();
}

export default app;
