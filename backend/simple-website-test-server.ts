import express from 'express';
import cors from 'cors';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// MySQL连接配置
const dbConfig = {
  host: process.env.MYSQL_HOST || '127.0.0.1',
  port: parseInt(process.env.MYSQL_PORT || '3306', 10),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'aitest',
  charset: 'utf8mb4'
};

// 创建数据库连接池
let pool: mysql.Pool;

async function initDatabase() {
  try {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
    
    console.log('✅ MySQL连接池已创建');
    
    // 测试连接
    const connection = await pool.getConnection();
    await connection.execute('SELECT 1');
    connection.release();
    console.log('✅ MySQL连接测试成功');
  } catch (error) {
    console.error('❌ MySQL连接失败:', error);
    console.log('⚠️  将使用内存存储模式');
  }
}

// 内存存储（当MySQL不可用时）
const memoryStorage = {
  testResults: new Map(),
  linkDetails: new Map(),
  errorLogs: new Map()
};

// 简化的网站测试函数
async function testWebsite(url: string): Promise<any> {
  const testId = uuidv4();
  const startTime = new Date();
  
  console.log(`🚀 开始测试网站: ${url}`);
  
  try {
    // 测试主页
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; WebsiteTestBot/1.0)'
      }
    });
    
    const endTime = new Date();
    const responseTime = endTime.getTime() - startTime.getTime();
    
    // 改进的链接提取
    const html = response.data;
    console.log(`📄 获取到HTML内容长度: ${html.length} 字符`);

    // 多种链接提取模式
    const linkPatterns = [
      /<a[^>]+href=["']([^"']+)["'][^>]*>/gi,           // 标准a标签
      /<link[^>]+href=["']([^"']+)["'][^>]*>/gi,        // link标签
      /href=["']([^"']+)["']/gi,                        // 任何href属性
      /src=["']([^"']+\.(?:js|css|png|jpg|jpeg|gif|svg))["']/gi, // 资源文件
    ];

    const links = [];
    const foundUrls = new Set();

    // 使用多种模式提取链接
    for (const pattern of linkPatterns) {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        const href = match[1];
        if (href && !href.startsWith('#') && !href.startsWith('javascript:') && !href.startsWith('mailto:')) {
          try {
            const fullUrl = href.startsWith('http') ? href : new URL(href, url).toString();
            if (!foundUrls.has(fullUrl)) {
              foundUrls.add(fullUrl);
              links.push(fullUrl);
            }
          } catch (e) {
            // 忽略无效URL
          }
        }
      }
    }

    console.log(`🔗 提取到 ${links.length} 个链接`);

    // 如果没有找到链接，添加一些常见的测试路径
    if (links.length === 0) {
      const commonPaths = [
        '/about',
        '/contact',
        '/services',
        '/products',
        '/blog',
        '/news',
        '/help',
        '/support',
        '/login',
        '/register'
      ];

      console.log(`🔍 未找到链接，尝试常见路径...`);
      for (const path of commonPaths) {
        try {
          const testUrl = new URL(path, url).toString();
          links.push(testUrl);
        } catch (e) {
          // 忽略错误
        }
      }
    }
    
    // 去重并限制数量
    const uniqueLinks = [...new Set(links)].slice(0, 20);
    
    // 测试链接
    const linkResults = [];
    let successfulLinks = 0;
    let failedLinks = 0;
    
    for (const link of uniqueLinks) {
      try {
        const linkResponse = await axios.head(link, {
          timeout: 5000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; WebsiteTestBot/1.0)'
          }
        });
        
        linkResults.push({
          url: link,
          status: linkResponse.status,
          success: true,
          responseTime: 0
        });
        successfulLinks++;
      } catch (error: any) {
        linkResults.push({
          url: link,
          status: error.response?.status || 0,
          success: false,
          error: error.message,
          responseTime: 0
        });
        failedLinks++;
      }
    }
    
    const testResult = {
      id: testId,
      websiteUrl: url,
      testType: 'full_test',
      status: 'completed',
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      totalLinks: uniqueLinks.length,
      successfulLinks,
      failedLinks,
      serverErrors: linkResults.filter(r => r.status >= 500).length,
      frontendErrors: 0,
      responseTime,
      links: linkResults
    };
    
    // 保存到数据库或内存
    await saveTestResult(testResult);
    
    console.log(`✅ 网站测试完成: ${url} (${successfulLinks}/${uniqueLinks.length} 链接成功)`);
    
    return testResult;
    
  } catch (error: any) {
    const endTime = new Date();
    const testResult = {
      id: testId,
      websiteUrl: url,
      testType: 'full_test',
      status: 'failed',
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      totalLinks: 0,
      successfulLinks: 0,
      failedLinks: 0,
      serverErrors: 1,
      frontendErrors: 0,
      error: error.message,
      links: []
    };
    
    await saveTestResult(testResult);
    
    console.log(`❌ 网站测试失败: ${url} - ${error.message}`);
    
    return testResult;
  }
}

// 保存测试结果
async function saveTestResult(result: any) {
  if (pool) {
    try {
      await pool.execute(`
        INSERT INTO website_test_results (
          id, website_url, test_type, status, start_time, end_time,
          total_links, successful_links, failed_links, server_errors, frontend_errors
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        result.id, result.websiteUrl, result.testType, result.status,
        new Date(result.startTime), result.endTime ? new Date(result.endTime) : null, result.totalLinks,
        result.successfulLinks, result.failedLinks, result.serverErrors, result.frontendErrors
      ]);
    } catch (error) {
      console.error('保存到数据库失败，使用内存存储:', error);
      memoryStorage.testResults.set(result.id, result);
    }
  } else {
    memoryStorage.testResults.set(result.id, result);
  }
}

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/test-interface.html'));
});

// API路由
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    database: pool ? 'connected' : 'memory',
    message: 'Website Test API is running'
  });
});

// webopte.com专用测试接口
app.post('/api/website-test/webopte', async (req, res) => {
  try {
    console.log('🎯 执行webopte.com专用测试');
    const result = await testWebsite('https://www.webopte.com/');
    
    res.json({
      success: true,
      data: result,
      message: 'webopte.com测试完成'
    });
  } catch (error: any) {
    console.error('webopte.com测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'webopte.com测试失败'
    });
  }
});

// 通用网站测试接口
app.post('/api/website-test/execute', async (req, res) => {
  try {
    const { websiteUrl } = req.body;
    
    if (!websiteUrl) {
      return res.status(400).json({
        success: false,
        error: '缺少websiteUrl参数'
      });
    }
    
    console.log(`🎯 执行网站测试: ${websiteUrl}`);
    const result = await testWebsite(websiteUrl);
    
    res.json({
      success: true,
      data: result,
      message: '网站测试完成'
    });
    return;
  } catch (error: any) {
    console.error('网站测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: '网站测试失败'
    });
    return;
  }
});

// 获取测试结果
app.get('/api/website-test/result/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    let result = null;
    
    if (pool) {
      try {
        const [rows] = await pool.execute(
          'SELECT * FROM website_test_results WHERE id = ?',
          [id]
        );
        result = (rows as any[])[0];
      } catch (error) {
        console.error('从数据库获取结果失败:', error);
      }
    }
    
    if (!result) {
      result = memoryStorage.testResults.get(id);
    }
    
    if (!result) {
      return res.status(404).json({
        success: false,
        error: '测试结果未找到'
      });
    }
    
    res.json({
      success: true,
      data: result
    });
    return;
  } catch (error: any) {
    console.error('获取测试结果失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
    return;
  }
});

// 获取测试历史
app.get('/api/website-test/history', async (req, res) => {
  try {
    let results = [];
    
    if (pool) {
      try {
        const [rows] = await pool.execute(
          'SELECT * FROM website_test_results ORDER BY start_time DESC LIMIT 50'
        );
        results = rows as any[];
      } catch (error) {
        console.error('从数据库获取历史失败:', error);
      }
    }
    
    if (results.length === 0) {
      results = Array.from(memoryStorage.testResults.values())
        .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
        .slice(0, 50);
    }
    
    res.json({
      success: true,
      data: {
        tests: results,
        pagination: {
          page: 1,
          limit: 50,
          total: results.length
        }
      }
    });
  } catch (error: any) {
    console.error('获取测试历史失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
async function startServer() {
  await initDatabase();
  
  app.listen(PORT, () => {
    console.log(`🚀 简化版网站测试服务器运行在端口 ${PORT}`);
    console.log(`📊 健康检查: http://localhost:${PORT}/health`);
    console.log(`🎯 webopte测试: POST http://localhost:${PORT}/api/website-test/webopte`);
    console.log(`🌐 通用测试: POST http://localhost:${PORT}/api/website-test/execute`);
  });
}

startServer().catch(console.error);
