import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';
import { ApiResponse } from '@shared/types';
import { Logger } from '../utils/logger';

const logger = new Logger('ValidationMiddleware');

/**
 * 请求验证中间件
 */
export function validateRequest(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // 验证请求体
      const validatedData = schema.parse(req.body);
      
      // 将验证后的数据替换原始请求体
      req.body = validatedData;
      
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));

        logger.warn('Request validation failed', { 
          url: req.url, 
          method: req.method,
          errors: validationErrors 
        });

        res.status(400).json({
          success: false,
          error: 'Validation failed',
          message: 'Request data validation failed',
          details: validationErrors,
        } as ApiResponse);
      } else {
        logger.error('Unexpected validation error', error);
        res.status(500).json({
          success: false,
          error: 'Internal validation error',
          message: 'An unexpected error occurred during validation',
        } as ApiResponse);
      }
    }
  };
}

/**
 * 查询参数验证中间件
 */
export function validateQuery(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedQuery = schema.parse(req.query);
      req.query = validatedQuery;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));

        logger.warn('Query validation failed', { 
          url: req.url, 
          method: req.method,
          errors: validationErrors 
        });

        res.status(400).json({
          success: false,
          error: 'Query validation failed',
          message: 'Query parameters validation failed',
          details: validationErrors,
        } as ApiResponse);
      } else {
        logger.error('Unexpected query validation error', error);
        res.status(500).json({
          success: false,
          error: 'Internal validation error',
          message: 'An unexpected error occurred during query validation',
        } as ApiResponse);
      }
    }
  };
}

/**
 * 路径参数验证中间件
 */
export function validateParams(schema: ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedParams = schema.parse(req.params);
      req.params = validatedParams;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));

        logger.warn('Params validation failed', { 
          url: req.url, 
          method: req.method,
          errors: validationErrors 
        });

        res.status(400).json({
          success: false,
          error: 'Parameters validation failed',
          message: 'Path parameters validation failed',
          details: validationErrors,
        } as ApiResponse);
      } else {
        logger.error('Unexpected params validation error', error);
        res.status(500).json({
          success: false,
          error: 'Internal validation error',
          message: 'An unexpected error occurred during parameters validation',
        } as ApiResponse);
      }
    }
  };
}
