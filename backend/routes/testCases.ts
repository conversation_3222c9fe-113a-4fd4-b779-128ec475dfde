import express from 'express';
import { TestCaseController } from '../controllers/TestCaseController';
import { validateRequest } from '../middleware/validation';
import { z } from 'zod';

const router = express.Router();
const testCaseController = new TestCaseController();

// 验证模式
const createTestCaseSchema = z.object({
  name: z.string().min(1, 'Test case name is required'),
  description: z.string().optional(),
  steps: z.array(z.object({
    description: z.string().min(1, 'Step description is required'),
    action: z.string(),
    target: z.string().optional(),
    value: z.string().optional(),
    expectedResult: z.string().optional(),
    order: z.number(),
  })),
  tags: z.array(z.string()).optional(),
});

const updateTestCaseSchema = createTestCaseSchema.partial();

/**
 * POST /api/test-cases
 * 创建测试用例
 */
router.post('/',
  validateRequest(createTestCaseSchema),
  testCaseController.createTestCase.bind(testCaseController)
);

/**
 * GET /api/test-cases
 * 获取所有测试用例
 */
router.get('/',
  testCaseController.getTestCases.bind(testCaseController)
);

/**
 * GET /api/test-cases/:id
 * 获取单个测试用例
 */
router.get('/:id',
  testCaseController.getTestCase.bind(testCaseController)
);

/**
 * PUT /api/test-cases/:id
 * 更新测试用例
 */
router.put('/:id',
  validateRequest(updateTestCaseSchema),
  testCaseController.updateTestCase.bind(testCaseController)
);

/**
 * DELETE /api/test-cases/:id
 * 删除测试用例
 */
router.delete('/:id',
  testCaseController.deleteTestCase.bind(testCaseController)
);

/**
 * POST /api/test-cases/:id/execute
 * 执行指定测试用例
 */
router.post('/:id/execute',
  testCaseController.executeTestCase.bind(testCaseController)
);

export default router;
