import express from 'express';
import { AutomationController } from '../controllers/AutomationController';
import { validateRequest } from '../middleware/validation';
import { z } from 'zod';

const router = express.Router();
const automationController = new AutomationController();

// 验证模式
const executeTestSchema = z.object({
  steps: z.array(z.string().min(1, 'Step cannot be empty')),
  testCaseId: z.string().optional(),
  config: z.object({
    aiProvider: z.object({
      name: z.enum(['openai', 'anthropic']),
      model: z.string(),
      apiKey: z.string(),
    }).optional(),
    browserConfig: z.object({
      headless: z.boolean().optional(),
      viewport: z.object({
        width: z.number().optional(),
        height: z.number().optional(),
      }).optional(),
      timeout: z.number().optional(),
    }).optional(),
    saveScreenshots: z.boolean().optional(),
    generateReport: z.boolean().optional(),
  }).optional(),
});

const parseStepsSchema = z.object({
  steps: z.array(z.string().min(1, 'Step cannot be empty')),
});

/**
 * POST /api/automation/execute
 * 执行自动化测试
 */
router.post('/execute', 
  validateRequest(executeTestSchema),
  automationController.executeTest.bind(automationController)
);

/**
 * POST /api/automation/parse
 * 解析自然语言步骤
 */
router.post('/parse',
  validateRequest(parseStepsSchema),
  automationController.parseSteps.bind(automationController)
);

/**
 * GET /api/automation/execution/:id
 * 获取执行状态
 */
router.get('/execution/:id',
  automationController.getExecutionStatus.bind(automationController)
);

/**
 * POST /api/automation/execution/:id/cancel
 * 取消执行
 */
router.post('/execution/:id/cancel',
  automationController.cancelExecution.bind(automationController)
);

/**
 * GET /api/automation/executions
 * 获取执行历史
 */
router.get('/executions',
  automationController.getExecutionHistory.bind(automationController)
);

/**
 * GET /api/automation/execution/:id/report
 * 获取测试报告
 */
router.get('/execution/:id/report',
  automationController.getTestReport.bind(automationController)
);

/**
 * GET /api/automation/execution/:id/screenshots
 * 获取执行截图列表
 */
router.get('/execution/:id/screenshots',
  automationController.getExecutionScreenshots.bind(automationController)
);

/**
 * GET /api/automation/health
 * 健康检查
 */
router.get('/health',
  automationController.healthCheck.bind(automationController)
);

export default router;
