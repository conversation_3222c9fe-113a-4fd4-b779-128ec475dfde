import { Router } from 'express';
import { WebsiteTestController } from '../controllers/websiteTestController';
import { Logger } from '../utils/logger';

const router = Router();
const logger = new Logger('WebsiteTestRoutes');

// 创建控制器实例
let controller: WebsiteTestController;

// 初始化控制器的中间件
const initializeController = async (req: any, res: any, next: any) => {
  if (!controller) {
    try {
      controller = new WebsiteTestController();
      await controller.initialize();
      logger.info('Website test controller initialized');
    } catch (error) {
      logger.error('Failed to initialize website test controller', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to initialize service',
        timestamp: new Date()
      });
    }
  }
  next();
};

// 应用初始化中间件到所有路由
router.use(initializeController);

/**
 * @route POST /api/website-test/execute
 * @desc 执行网站测试
 * @access Public
 * @body {
 *   websiteUrl: string,
 *   testType: 'link_check' | 'error_monitor' | 'full_test',
 *   browserConfig?: object,
 *   crawlerConfig?: object,
 *   errorMonitorConfig?: object
 * }
 */
router.post('/execute', async (req, res) => {
  await controller.executeTest(req, res);
});

/**
 * @route GET /api/website-test/result/:testResultId
 * @desc 获取测试结果
 * @access Public
 * @param testResultId - 测试结果ID
 */
router.get('/result/:testResultId', async (req, res) => {
  await controller.getTestResult(req, res);
});

/**
 * @route GET /api/website-test/history
 * @desc 获取测试历史
 * @access Public
 * @query {
 *   page?: number,
 *   limit?: number,
 *   websiteUrl?: string,
 *   testType?: string,
 *   status?: string,
 *   startDate?: string,
 *   endDate?: string,
 *   sortBy?: string,
 *   sortOrder?: 'asc' | 'desc'
 * }
 */
router.get('/history', async (req, res) => {
  await controller.getTestHistory(req, res);
});

/**
 * @route POST /api/website-test/stop/:testResultId
 * @desc 停止正在运行的测试
 * @access Public
 * @param testResultId - 测试结果ID
 */
router.post('/stop/:testResultId', async (req, res) => {
  await controller.stopTest(req, res);
});

/**
 * @route GET /api/website-test/statistics
 * @desc 获取测试统计信息
 * @access Public
 */
router.get('/statistics', async (req, res) => {
  await controller.getTestStatistics(req, res);
});

/**
 * @route GET /api/website-test/health
 * @desc 健康检查
 * @access Public
 */
router.get('/health', async (req, res) => {
  await controller.healthCheck(req, res);
});

/**
 * @route POST /api/website-test/webopte
 * @desc 专门为webopte.com网站设计的测试接口
 * @access Public
 */
router.post('/webopte', async (req, res) => {
  try {
    logger.info('Executing webopte.com specific test');
    
    // 为webopte.com网站设置专门的测试配置
    const webopteConfig = {
      websiteUrl: 'https://www.webopte.com/',
      testType: 'full_test' as const,
      browserConfig: {
        headless: false,
        viewport: {
          width: 1920,
          height: 1080
        },
        timeout: 30000,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      crawlerConfig: {
        maxDepth: 3,
        maxPages: 50,
        followExternalLinks: false,
        respectRobotsTxt: true,
        delay: 2000,
        concurrent: 2
      },
      errorMonitorConfig: {
        captureScreenshots: true,
        captureConsoleLogs: true,
        captureNetworkErrors: true,
        monitorJavaScriptErrors: true
      }
    };

    // 将配置添加到请求体中
    req.body = webopteConfig;
    
    // 调用标准的执行测试方法
    await controller.executeTest(req, res);
  } catch (error) {
    logger.error('Failed to execute webopte.com test', error);
    res.status(500).json({
      success: false,
      error: (error as Error).message || 'Internal server error',
      timestamp: new Date()
    });
  }
});

/**
 * @route POST /api/website-test/batch
 * @desc 批量测试多个网站
 * @access Public
 * @body {
 *   websites: string[],
 *   config: WebsiteTestConfig,
 *   concurrent?: number
 * }
 */
router.post('/batch', async (req, res) => {
  try {
    const { websites, config, concurrent = 3 } = req.body;
    
    if (!websites || !Array.isArray(websites) || websites.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Websites array is required and must not be empty',
        timestamp: new Date()
      });
    }

    if (websites.length > 10) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 10 websites allowed per batch',
        timestamp: new Date()
      });
    }

    logger.info('Starting batch test', { websiteCount: websites.length });

    const results: any[] = [];
    const errors: any[] = [];

    // 并发执行测试（限制并发数）
    const chunks = [];
    for (let i = 0; i < websites.length; i += concurrent) {
      chunks.push(websites.slice(i, i + concurrent));
    }

    for (const chunk of chunks) {
      const promises = chunk.map(async (websiteUrl: string) => {
        try {
          const testConfig = { ...config, websiteUrl };
          const testResult = await (controller as any).testService.executeTest(testConfig);
          return { websiteUrl, success: true, testResultId: testResult.id };
        } catch (error) {
          logger.error('Batch test failed for website', { websiteUrl, error: (error as Error).message });
          return { websiteUrl, success: false, error: (error as Error).message };
        }
      });

      const chunkResults = await Promise.all(promises);
      
      chunkResults.forEach(result => {
        if (result.success) {
          results.push(result);
        } else {
          errors.push(result);
        }
      });
    }

    res.status(200).json({
      success: true,
      data: {
        successful: results,
        failed: errors,
        summary: {
          total: websites.length,
          successful: results.length,
          failed: errors.length
        }
      },
      timestamp: new Date()
    });
    return;

  } catch (error) {
    logger.error('Batch test execution failed', error);
    res.status(500).json({
      success: false,
      error: (error as Error).message || 'Internal server error',
      timestamp: new Date()
    });
    return;
  }
});

// 错误处理中间件
router.use((error: any, req: any, res: any, next: any) => {
  logger.error('Website test route error', error);
  
  if (!res.headersSent) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date()
    });
  }
});

// 清理函数
export const cleanup = async () => {
  if (controller) {
    await controller.cleanup();
    logger.info('Website test routes cleaned up');
  }
};

export default router;
