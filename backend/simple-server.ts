import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import { SimpleAutomationEngine, SimpleStepParser } from './simple-automation';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 简单的日志函数
const log = (message: string, data?: any) => {
  console.log(`[${new Date().toISOString()}] ${message}`, data || '');
};

// 存储活跃的执行
const activeExecutions = new Map<string, SimpleAutomationEngine>();
const stepParser = new SimpleStepParser();

// 中间件
app.use(helmet());
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志
app.use((req, res, next) => {
  log(`${req.method} ${req.url}`);
  next();
});

// 自动化测试路由
app.post('/api/automation/execute', async (req, res): Promise<void> => {
  try {
    const { steps } = req.body;

    if (!steps || !Array.isArray(steps) || steps.length === 0) {
      res.status(400).json({
        success: false,
        error: 'Steps are required and must be a non-empty array'
      });
      return;
    }

    const executionId = uuidv4();
    const engine = new SimpleAutomationEngine(executionId, steps);
    activeExecutions.set(executionId, engine);

    log('Starting test execution', { executionId, stepsCount: steps.length });

    // 异步执行测试
    engine.executeSteps().then(() => {
      log('Test execution completed', { executionId });
    }).catch((error) => {
      log('Test execution failed', { executionId, error: error.message });
    });

    res.json({
      success: true,
      data: {
        executionId,
        status: 'started',
        message: 'Test execution started successfully'
      },
      message: 'Test execution started'
    });

  } catch (error) {
    log('Execute test error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// 新增：URL验证和编排测试端点
app.post('/api/automation/execute-with-validation', async (req, res): Promise<void> => {
  try {
    const {
      steps,
      requestValidation,
      requestOrchestration
    } = req.body;

    if (!steps || !Array.isArray(steps) || steps.length === 0) {
      res.status(400).json({
        success: false,
        error: 'Steps are required and must be a non-empty array'
      });
      return;
    }

    const executionId = uuidv4();
    log('Starting enhanced test execution', {
      executionId,
      stepsCount: steps.length,
      hasValidation: !!requestValidation,
      hasOrchestration: !!requestOrchestration
    });

    // 创建增强的执行引擎（这里我们模拟实现）
    const enhancedEngine: any = {
      executionId,
      steps,
      requestValidation,
      requestOrchestration,
      status: 'running',
      results: [],
      validationResults: [],
      orchestrationResults: []
    };

    activeExecutions.set(executionId, enhancedEngine as any);

    // 模拟异步执行
    setTimeout(async () => {
      try {
        // 模拟URL验证结果
        if (requestValidation) {
          enhancedEngine.validationResults.push({
            url: requestValidation.url || 'https://example.com/api/data',
            method: 'GET',
            statusCode: 200,
            responseTime: 850,
            resultCount: 25,
            isValid: true,
            errors: [],
            warnings: [],
            timestamp: new Date()
          });
        }

        // 模拟编排执行
        if (requestOrchestration) {
          enhancedEngine.orchestrationResults.push({
            ruleId: requestOrchestration.id || 'auto-trigger-rule',
            triggered: true,
            targetUrl: requestOrchestration.targetUrl || 'https://example.com/api/process',
            success: true,
            timestamp: new Date()
          });
        }

        enhancedEngine.status = 'completed';
        enhancedEngine.completedAt = new Date().toISOString();
        log('Enhanced test execution completed', { executionId });
      } catch (error) {
        enhancedEngine.status = 'failed';
        enhancedEngine.completedAt = new Date().toISOString();
        log('Enhanced test execution failed', { executionId, error: (error as Error).message });
      }
    }, 2000);

    res.json({
      success: true,
      data: {
        executionId,
        status: 'started',
        message: 'Enhanced test execution started successfully',
        features: {
          requestValidation: !!requestValidation,
          requestOrchestration: !!requestOrchestration
        }
      },
      message: 'Enhanced test execution started'
    });

  } catch (error) {
    log('Enhanced execute test error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/automation/parse', async (req, res): Promise<void> => {
  try {
    const { steps } = req.body;

    if (!steps || !Array.isArray(steps)) {
      res.status(400).json({
        success: false,
        error: 'Steps are required and must be an array'
      });
      return;
    }

    const parsedSteps = await stepParser.parseSteps(steps);
    const validationResults = parsedSteps.map(step => ({
      step,
      validation: stepParser.validateStep(step)
    }));

    res.json({
      success: true,
      data: {
        parsedSteps,
        validationResults
      },
      message: 'Steps parsed successfully'
    });

  } catch (error) {
    log('Parse steps error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/automation/execution/:id', async (req, res): Promise<void> => {
  try {
    const { id } = req.params;

    // 记录请求（但不要太频繁）
    if (Math.random() < 0.1) { // 只记录10%的请求以减少日志噪音
      log(`Checking execution status for ID: ${id}`);
    }

    const engine = activeExecutions.get(id);

    if (!engine) {
      res.status(404).json({
        success: false,
        error: 'Execution not found',
        message: `No execution found with ID: ${id}`
      });
      return;
    }

    // 检查是否是新的增强执行引擎（普通对象）还是旧的SimpleAutomationEngine
    let execution;
    if (typeof engine === 'object' && 'executionId' in engine) {
      // 新的增强执行引擎
      execution = engine;

      // 如果执行已完成超过5分钟，自动清理
      if ((execution as any).status === 'completed' || (execution as any).status === 'failed') {
        const now = Date.now();
        const completedTime = new Date((execution as any).completedAt || now).getTime();
        if (now - completedTime > 5 * 60 * 1000) { // 5分钟
          activeExecutions.delete(id);
          log(`Cleaned up old execution: ${id}`);
        }
      }

    } else if (engine && typeof (engine as any).getCurrentExecution === 'function') {
      // 旧的SimpleAutomationEngine
      execution = (engine as any).getCurrentExecution();
    } else {
      res.status(500).json({
        success: false,
        error: 'Invalid execution engine'
      });
      return;
    }

    res.json({
      success: true,
      data: execution,
      message: 'Execution status retrieved'
    });

  } catch (error) {
    log('Get execution status error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/automation/execution/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;
    const engine = activeExecutions.get(id);

    if (engine) {
      activeExecutions.delete(id);
      log('Execution cancelled', { executionId: id });
      res.json({
        success: true,
        message: 'Execution cancelled successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Active execution not found',
        message: 'Execution not found or already completed'
      });
    }

  } catch (error) {
    log('Cancel execution error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/automation/executions', async (req, res) => {
  try {
    // 简化版：返回当前活跃的执行
    const executions = Array.from(activeExecutions.entries()).map(([executionId, engine]) => {
      const execution = engine.getCurrentExecution();
      return {
        ...execution,
        id: executionId
      };
    });

    res.json({
      success: true,
      data: executions,
      message: 'Execution history retrieved'
    });

  } catch (error) {
    log('Get execution history error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// 获取执行的详细错误信息
app.get('/api/automation/execution/:id/errors', async (req, res): Promise<void> => {
  try {
    const { id } = req.params;
    const engine = activeExecutions.get(id);

    if (!engine) {
      res.status(404).json({
        success: false,
        error: 'Execution not found'
      });
      return;
    }

    let execution;
    if (typeof engine === 'object' && 'executionId' in engine) {
      execution = engine;
    } else if (engine && typeof (engine as any).getCurrentExecution === 'function') {
      execution = (engine as any).getCurrentExecution();
    } else {
      res.status(500).json({
        success: false,
        error: 'Invalid execution engine'
      });
      return;
    }

    // 提取失败步骤的详细信息
    const failedSteps = execution.results?.filter((result: any) => result.status === 'failed') || [];
    const errorDetails = failedSteps.map((step: any) => ({
      stepId: step.stepId,
      stepIndex: parseInt(step.stepId.replace('step-', '')) + 1,
      stepDescription: execution.steps?.[parseInt(step.stepId.replace('step-', ''))] || '未知步骤',
      status: step.status,
      startTime: step.startTime,
      endTime: step.endTime,
      logs: step.logs || [],
      screenshot: step.screenshot,
      errorMessage: step.error || '步骤执行失败，但未提供具体错误信息',
      possibleCauses: [
        '网络连接问题',
        '页面加载超时',
        '元素定位失败',
        '页面结构发生变化',
        '服务器响应异常'
      ],
      suggestions: [
        '检查网络连接是否正常',
        '确认目标URL是否可访问',
        '检查页面是否需要登录',
        '验证页面元素是否存在',
        '增加等待时间'
      ]
    }));

    res.json({
      success: true,
      data: {
        executionId: id,
        totalSteps: execution.results?.length || 0,
        failedSteps: failedSteps.length,
        errorDetails,
        executionStatus: execution.status,
        executionTime: execution.endTime ?
          new Date(execution.endTime).getTime() - new Date(execution.startTime || execution.endTime).getTime() :
          null
      },
      message: 'Error details retrieved successfully'
    });

  } catch (error) {
    log('Get error details error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// 清理旧的执行记录
app.post('/api/automation/cleanup', async (req, res): Promise<void> => {
  try {
    const beforeCount = activeExecutions.size;
    const now = Date.now();
    const cutoffTime = now - 5 * 60 * 1000; // 5分钟前

    for (const [id, engine] of activeExecutions.entries()) {
      if (typeof engine === 'object' && 'status' in engine) {
        if (((engine as any).status === 'completed' || (engine as any).status === 'failed') && (engine as any).completedAt) {
          const completedTime = new Date((engine as any).completedAt).getTime();
          if (completedTime < cutoffTime) {
            activeExecutions.delete(id);
          }
        }
      }
    }

    const afterCount = activeExecutions.size;
    const cleanedCount = beforeCount - afterCount;

    res.json({
      success: true,
      message: `Cleaned up ${cleanedCount} old executions`,
      data: {
        before: beforeCount,
        after: afterCount,
        cleaned: cleanedCount
      }
    });

    log(`Cleanup completed: removed ${cleanedCount} old executions`);

  } catch (error) {
    log('Cleanup error', error);
    res.status(500).json({ success: false, error: 'Cleanup failed' });
  }
});

app.get('/api/automation/health', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        activeExecutions: activeExecutions.size,
        uptime: process.uptime(),
        memory: process.memoryUsage()
      },
      message: 'Service is healthy'
    });

  } catch (error) {
    log('Health check error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// 测试用例路由（简化版）
app.post('/api/test-cases', async (req, res) => {
  try {
    res.json({
      success: true,
      data: { id: uuidv4(), ...req.body },
      message: 'Test case created successfully (demo)'
    });
  } catch (error) {
    log('Create test case error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/test-cases', async (req, res) => {
  try {
    res.json({
      success: true,
      data: [],
      message: 'Test cases retrieved successfully (demo)'
    });
  } catch (error) {
    log('Get test cases error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/test-cases/:id', async (req, res) => {
  try {
    res.json({
      success: true,
      data: { id: req.params.id, name: 'Demo Test Case' },
      message: 'Test case retrieved successfully (demo)'
    });
  } catch (error) {
    log('Get test case error', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API信息
app.get('/api', (req, res) => {
  res.json({
    name: 'AI Browser Automation Agent API',
    version: '1.0.0',
    description: '基于浏览器的自动化测试AI智能体API（简化版）',
    endpoints: {
      automation: {
        execute: 'POST /api/automation/execute',
        parse: 'POST /api/automation/parse',
        status: 'GET /api/automation/execution/:id',
        cancel: 'POST /api/automation/execution/:id/cancel',
        history: 'GET /api/automation/executions',
        health: 'GET /api/automation/health',
      },
      testCases: {
        create: 'POST /api/test-cases',
        list: 'GET /api/test-cases',
        get: 'GET /api/test-cases/:id',
      },
    },
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.url}`,
  });
});

// 错误处理
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  log('Unhandled error', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message,
  });
});

// 启动服务器
app.listen(PORT, () => {
  log(`Server started on port ${PORT}`);
  log(`API documentation: http://localhost:${PORT}/api`);
  log(`Health check: http://localhost:${PORT}/health`);
  log('简化版AI自动化测试服务器已启动！');
});

// 优雅关闭
process.on('SIGTERM', () => {
  log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
