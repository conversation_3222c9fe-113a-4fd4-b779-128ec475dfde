import dotenv from 'dotenv';
import { AppConfig } from '@shared/types';
import { MySQLConfig } from './database';

// 加载环境变量
dotenv.config();

export const config: AppConfig = {
  server: {
    port: parseInt(process.env.PORT || '3001', 10),
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  },
  ai: {
    defaultProvider: (process.env.DEFAULT_AI_PROVIDER as 'openai' | 'anthropic') || 'openai',
    defaultModel: process.env.DEFAULT_AI_MODEL || 'gpt-4',
    timeout: parseInt(process.env.AI_TIMEOUT || '60000', 10),
    maxRetries: parseInt(process.env.MAX_RETRY_ATTEMPTS || '3', 10),
  },
  browser: {
    headless: process.env.HEADLESS === 'true',
    timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000', 10),
    viewport: {
      width: parseInt(process.env.DEFAULT_VIEWPORT_WIDTH || '1920', 10),
      height: parseInt(process.env.DEFAULT_VIEWPORT_HEIGHT || '1080', 10),
    },
  },
  storage: {
    databasePath: process.env.DATABASE_PATH || './data/automation.db',
    screenshotDir: process.env.SCREENSHOT_DIR || './screenshots',
    uploadDir: process.env.UPLOAD_DIR || './uploads',
  },
  // 新增MySQL配置
  mysql: {
    host: process.env.MYSQL_HOST || '127.0.0.1',
    port: parseInt(process.env.MYSQL_PORT || '3306', 10),
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'aitest',
    connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10', 10),
    acquireTimeout: parseInt(process.env.MYSQL_ACQUIRE_TIMEOUT || '60000', 10),
    timeout: parseInt(process.env.MYSQL_TIMEOUT || '60000', 10),
    reconnect: true,
    charset: 'utf8mb4'
  },
};

// 验证必要的环境变量
export function validateConfig(): void {
  const requiredEnvVars = [];
  
  if (!process.env.OPENAI_API_KEY && !process.env.ANTHROPIC_API_KEY) {
    requiredEnvVars.push('OPENAI_API_KEY or ANTHROPIC_API_KEY');
  }
  
  if (requiredEnvVars.length > 0) {
    throw new Error(`Missing required environment variables: ${requiredEnvVars.join(', ')}`);
  }
}

// AI提供商配置
export const getAIProviderConfig = () => {
  const openaiKey = process.env.OPENAI_API_KEY;
  const anthropicKey = process.env.ANTHROPIC_API_KEY;
  
  return {
    openai: openaiKey ? {
      name: 'openai' as const,
      model: process.env.DEFAULT_AI_MODEL || 'gpt-4',
      apiKey: openaiKey,
    } : null,
    anthropic: anthropicKey ? {
      name: 'anthropic' as const,
      model: 'claude-3-sonnet-20240229',
      apiKey: anthropicKey,
    } : null,
  };
};

// Browserbase配置（可选）
export const getBrowserbaseConfig = () => {
  const apiKey = process.env.BROWSERBASE_API_KEY;
  const projectId = process.env.BROWSERBASE_PROJECT_ID;
  
  if (apiKey && projectId) {
    return {
      apiKey,
      projectId,
    };
  }
  
  return null;
};
