import mysql from 'mysql2/promise';
import { Logger } from '../utils/logger';

// MySQL数据库配置接口
export interface MySQLConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
  acquireTimeout: number;
  timeout: number;
  reconnect: boolean;
  charset: string;
}

// 默认MySQL配置
export const defaultMySQLConfig: MySQLConfig = {
  host: process.env.MYSQL_HOST || '127.0.0.1',
  port: parseInt(process.env.MYSQL_PORT || '3306', 10),
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'aitest',
  connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10', 10),
  acquireTimeout: parseInt(process.env.MYSQL_ACQUIRE_TIMEOUT || '60000', 10),
  timeout: parseInt(process.env.MYSQL_TIMEOUT || '60000', 10),
  reconnect: true,
  charset: 'utf8mb4'
};

// 创建MySQL连接池
export function createMySQLPool(config: MySQLConfig = defaultMySQLConfig): mysql.Pool {
  const logger = new Logger('MySQLPool');
  
  try {
    const pool = mysql.createPool({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      waitForConnections: true,
      connectionLimit: config.connectionLimit,
      queueLimit: 0,
      acquireTimeout: config.acquireTimeout,
      timeout: config.timeout,
      reconnect: config.reconnect,
      charset: config.charset,
      // 启用多语句查询
      multipleStatements: true,
      // 时区设置
      timezone: '+08:00',
      // SSL配置（生产环境建议启用）
      ssl: process.env.NODE_ENV === 'production' ? {
        rejectUnauthorized: false
      } : false
    });

    logger.info('MySQL connection pool created successfully', {
      host: config.host,
      port: config.port,
      database: config.database,
      connectionLimit: config.connectionLimit
    });

    return pool;
  } catch (error) {
    logger.error('Failed to create MySQL connection pool', error);
    throw error;
  }
}

// 测试数据库连接
export async function testMySQLConnection(pool: mysql.Pool): Promise<boolean> {
  const logger = new Logger('MySQLConnection');
  
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    
    logger.info('MySQL connection test successful');
    return true;
  } catch (error) {
    logger.error('MySQL connection test failed', error);
    return false;
  }
}

// 初始化数据库表结构
export async function initializeMySQLTables(pool: mysql.Pool): Promise<void> {
  const logger = new Logger('MySQLTables');
  
  try {
    const connection = await pool.getConnection();
    
    // 创建网站测试结果表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS website_test_results (
        id VARCHAR(36) PRIMARY KEY,
        website_url VARCHAR(2048) NOT NULL,
        test_type ENUM('link_check', 'error_monitor', 'full_test') NOT NULL,
        status ENUM('pending', 'running', 'completed', 'failed') NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NULL,
        total_links INT DEFAULT 0,
        successful_links INT DEFAULT 0,
        failed_links INT DEFAULT 0,
        server_errors INT DEFAULT 0,
        frontend_errors INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_website_url (website_url(255)),
        INDEX idx_status (status),
        INDEX idx_test_type (test_type),
        INDEX idx_start_time (start_time)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建链接测试详情表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS link_test_details (
        id VARCHAR(36) PRIMARY KEY,
        test_result_id VARCHAR(36) NOT NULL,
        url VARCHAR(2048) NOT NULL,
        http_status INT NULL,
        response_time INT NULL,
        error_message TEXT NULL,
        server_error BOOLEAN DEFAULT FALSE,
        frontend_error BOOLEAN DEFAULT FALSE,
        screenshot_path VARCHAR(512) NULL,
        tested_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (test_result_id) REFERENCES website_test_results(id) ON DELETE CASCADE,
        INDEX idx_test_result_id (test_result_id),
        INDEX idx_url (url(255)),
        INDEX idx_http_status (http_status),
        INDEX idx_tested_at (tested_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建错误日志表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS error_logs (
        id VARCHAR(36) PRIMARY KEY,
        test_result_id VARCHAR(36) NULL,
        link_detail_id VARCHAR(36) NULL,
        error_type ENUM('server_error', 'frontend_error', 'network_error', 'timeout_error') NOT NULL,
        error_code VARCHAR(50) NULL,
        error_message TEXT NOT NULL,
        stack_trace TEXT NULL,
        url VARCHAR(2048) NULL,
        user_agent VARCHAR(512) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (test_result_id) REFERENCES website_test_results(id) ON DELETE SET NULL,
        FOREIGN KEY (link_detail_id) REFERENCES link_test_details(id) ON DELETE SET NULL,
        INDEX idx_test_result_id (test_result_id),
        INDEX idx_error_type (error_type),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    connection.release();
    logger.info('MySQL tables initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize MySQL tables', error);
    throw error;
  }
}
