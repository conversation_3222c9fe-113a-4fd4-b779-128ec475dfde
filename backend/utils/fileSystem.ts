import fs from 'fs/promises';
import path from 'path';
import { Logger } from './logger';

const logger = new Logger('FileSystem');

/**
 * 确保目录存在，如果不存在则创建
 */
export async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await fs.access(dirPath);
  } catch (error) {
    // 目录不存在，创建它
    try {
      await fs.mkdir(dirPath, { recursive: true });
      logger.info(`Created directory: ${dirPath}`);
    } catch (createError) {
      logger.error(`Failed to create directory: ${dirPath}`, createError);
      throw createError;
    }
  }
}

/**
 * 初始化应用程序所需的目录结构
 */
export async function initializeDirectories(): Promise<void> {
  const directories = [
    './data',
    './logs',
    './screenshots',
    './uploads',
  ];

  for (const dir of directories) {
    await ensureDirectoryExists(dir);
  }
}

/**
 * 安全地删除文件
 */
export async function safeDeleteFile(filePath: string): Promise<boolean> {
  try {
    await fs.unlink(filePath);
    logger.info(`Deleted file: ${filePath}`);
    return true;
  } catch (error) {
    logger.warn(`Failed to delete file: ${filePath}`, error);
    return false;
  }
}

/**
 * 获取文件大小
 */
export async function getFileSize(filePath: string): Promise<number> {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    logger.error(`Failed to get file size: ${filePath}`, error);
    return 0;
  }
}

/**
 * 检查文件是否存在
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 清理旧文件（基于创建时间）
 */
export async function cleanupOldFiles(
  directory: string, 
  maxAgeMs: number
): Promise<number> {
  try {
    const files = await fs.readdir(directory);
    let deletedCount = 0;
    const now = Date.now();

    for (const file of files) {
      const filePath = path.join(directory, file);
      const stats = await fs.stat(filePath);
      
      if (now - stats.mtime.getTime() > maxAgeMs) {
        const deleted = await safeDeleteFile(filePath);
        if (deleted) deletedCount++;
      }
    }

    if (deletedCount > 0) {
      logger.info(`Cleaned up ${deletedCount} old files from ${directory}`);
    }

    return deletedCount;
  } catch (error) {
    logger.error(`Failed to cleanup old files in ${directory}`, error);
    return 0;
  }
}

/**
 * 获取目录中的文件列表
 */
export async function getFilesInDirectory(
  directory: string,
  extension?: string
): Promise<string[]> {
  try {
    const files = await fs.readdir(directory);
    
    if (extension) {
      return files.filter(file => path.extname(file) === extension);
    }
    
    return files;
  } catch (error) {
    logger.error(`Failed to read directory: ${directory}`, error);
    return [];
  }
}
