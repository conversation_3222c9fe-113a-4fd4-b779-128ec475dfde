import winston from 'winston';
import path from 'path';

// 日志级别配置
const logLevel = process.env.LOG_LEVEL || 'info';
const logFile = process.env.LOG_FILE || './logs/app.log';

// 确保日志目录存在
const logDir = path.dirname(logFile);

// 创建Winston logger实例
const winstonLogger = winston.createLogger({
  level: logLevel,
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ai-automation-agent' },
  transports: [
    // 文件日志
    new winston.transports.File({ 
      filename: logFile.replace('.log', '-error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: logFile 
    }),
  ],
});

// 开发环境下添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  winstonLogger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
      })
    )
  }));
}

export class Logger {
  private context: string;

  constructor(context: string = 'App') {
    this.context = context;
  }

  private log(level: string, message: string, meta?: any) {
    winstonLogger.log(level, message, {
      context: this.context,
      ...meta
    });
  }

  info(message: string, meta?: any) {
    this.log('info', message, meta);
  }

  error(message: string, error?: any, meta?: any) {
    const errorMeta = error instanceof Error ? {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      }
    } : { error };

    this.log('error', message, { ...errorMeta, ...meta });
  }

  warn(message: string, meta?: any) {
    this.log('warn', message, meta);
  }

  debug(message: string, meta?: any) {
    this.log('debug', message, meta);
  }

  verbose(message: string, meta?: any) {
    this.log('verbose', message, meta);
  }
}

// 导出默认logger实例
export const logger = new Logger();

// 导出Winston实例供其他地方使用
export { winstonLogger };
