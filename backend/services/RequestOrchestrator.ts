import { Page } from 'playwright';
import { 
  RequestOrchestrationRule, 
  RequestOrchestrationContext, 
  RequestValidationResult,
  NetworkRequest,
  NetworkResponse 
} from '@shared/types';
import { Logger } from '../utils/logger';
import { ResultCapture } from './ResultCapture';

export class RequestOrchestrator {
  private logger: Logger;
  private rules: Map<string, RequestOrchestrationRule> = new Map();
  private context: RequestOrchestrationContext;
  private resultCapture: ResultCapture;
  private isExecuting: boolean = false;

  constructor(private page: Page, resultCapture: ResultCapture) {
    this.logger = new Logger('RequestOrchestrator');
    this.resultCapture = resultCapture;
    this.context = {
      executionId: '',
      currentStep: 0,
      totalSteps: 0,
      variables: {},
      results: [],
      errors: []
    };
  }

  /**
   * 添加编排规则
   */
  addRule(rule: RequestOrchestrationRule): void {
    this.rules.set(rule.id, rule);
    this.logger.debug('Added orchestration rule', { ruleId: rule.id, ruleName: rule.name });
  }

  /**
   * 移除编排规则
   */
  removeRule(ruleId: string): void {
    this.rules.delete(ruleId);
    this.logger.debug('Removed orchestration rule', { ruleId });
  }

  /**
   * 初始化执行上下文
   */
  initializeContext(executionId: string): void {
    this.context = {
      executionId,
      currentStep: 0,
      totalSteps: this.rules.size,
      variables: {},
      results: [],
      errors: []
    };
    this.logger.info('Orchestration context initialized', { executionId, totalRules: this.rules.size });
  }

  /**
   * 检查并执行触发的规则
   */
  async checkAndExecuteTriggers(validationResult: RequestValidationResult): Promise<void> {
    if (this.isExecuting) {
      this.logger.warn('Orchestrator is already executing, skipping trigger check');
      return;
    }

    const triggeredRules = this.findTriggeredRules(validationResult);
    
    if (triggeredRules.length > 0) {
      this.logger.info('Found triggered rules', { 
        count: triggeredRules.length, 
        ruleIds: triggeredRules.map(r => r.id) 
      });
      
      await this.executeRules(triggeredRules);
    }
  }

  /**
   * 查找被触发的规则
   */
  private findTriggeredRules(validationResult: RequestValidationResult): RequestOrchestrationRule[] {
    const triggeredRules: RequestOrchestrationRule[] = [];
    
    for (const rule of this.rules.values()) {
      if (this.isRuleTriggered(rule, validationResult)) {
        triggeredRules.push(rule);
      }
    }
    
    return triggeredRules;
  }

  /**
   * 检查规则是否被触发
   */
  private isRuleTriggered(rule: RequestOrchestrationRule, validationResult: RequestValidationResult): boolean {
    const { trigger } = rule;
    
    switch (trigger.type) {
      case 'url_success':
        return trigger.sourceUrl === validationResult.url && validationResult.isValid;
        
      case 'url_failure':
        return trigger.sourceUrl === validationResult.url && !validationResult.isValid;
        
      case 'condition':
        return this.evaluateCondition(trigger.condition || '', validationResult);
        
      case 'manual':
        return false; // 手动触发不在此处理
        
      default:
        return false;
    }
  }

  /**
   * 评估条件表达式
   */
  private evaluateCondition(condition: string, validationResult: RequestValidationResult): boolean {
    try {
      // 创建安全的评估上下文
      const context = {
        url: validationResult.url,
        statusCode: validationResult.statusCode,
        responseTime: validationResult.responseTime,
        isValid: validationResult.isValid,
        resultCount: validationResult.resultCount,
        errors: validationResult.errors,
        variables: this.context.variables
      };
      
      // 简单的条件评估（生产环境中应使用更安全的表达式引擎）
      const func = new Function('context', `with(context) { return ${condition}; }`);
      return Boolean(func(context));
      
    } catch (error) {
      this.logger.error('Failed to evaluate condition', { condition, error });
      return false;
    }
  }

  /**
   * 执行规则列表
   */
  private async executeRules(rules: RequestOrchestrationRule[]): Promise<void> {
    this.isExecuting = true;
    
    try {
      for (const rule of rules) {
        await this.executeRule(rule);
      }
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 执行单个规则
   */
  private async executeRule(rule: RequestOrchestrationRule): Promise<void> {
    this.logger.info('Executing orchestration rule', { ruleId: rule.id, ruleName: rule.name });
    
    try {
      // 延迟执行
      if (rule.execution.delay && rule.execution.delay > 0) {
        this.logger.debug('Delaying rule execution', { delay: rule.execution.delay });
        await new Promise(resolve => setTimeout(resolve, rule.execution.delay));
      }
      
      // 准备请求数据
      const requestData = await this.prepareRequestData(rule);
      
      // 执行请求
      const result = await this.executeRequest(rule, requestData);
      
      // 处理结果
      this.context.results.push(result);
      this.context.currentStep++;
      
      // 数据映射
      if (rule.dataMapping && result.responseData) {
        this.applyDataMapping(rule.dataMapping, result.responseData);
      }
      
      this.logger.info('Rule executed successfully', { 
        ruleId: rule.id, 
        isValid: result.isValid 
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.context.errors.push(`Rule ${rule.id} failed: ${errorMessage}`);
      
      this.logger.error('Rule execution failed', { 
        ruleId: rule.id, 
        error: errorMessage 
      });
      
      // 检查是否继续执行
      if (!rule.execution.continueOnFailure) {
        throw error;
      }
    }
  }

  /**
   * 准备请求数据
   */
  private async prepareRequestData(rule: RequestOrchestrationRule): Promise<any> {
    let requestData = rule.targetRequest.body;
    
    // 替换变量
    if (requestData && typeof requestData === 'object') {
      requestData = this.replaceVariables(requestData);
    }
    
    return requestData;
  }

  /**
   * 替换变量
   */
  private replaceVariables(data: any): any {
    if (typeof data === 'string') {
      return data.replace(/\$\{(\w+)\}/g, (match, varName) => {
        return this.context.variables[varName] || match;
      });
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.replaceVariables(item));
    }
    
    if (typeof data === 'object' && data !== null) {
      const result: any = {};
      for (const [key, value] of Object.entries(data)) {
        result[key] = this.replaceVariables(value);
      }
      return result;
    }
    
    return data;
  }

  /**
   * 执行HTTP请求
   */
  private async executeRequest(rule: RequestOrchestrationRule, requestData: any): Promise<RequestValidationResult> {
    const { targetRequest } = rule;
    const startTime = Date.now();
    
    try {
      // 添加验证规则
      if (targetRequest.validation) {
        this.resultCapture.addValidationRule(targetRequest.url, targetRequest.validation);
      }
      
      // 执行页面导航或请求
      if (targetRequest.method === 'GET' || !targetRequest.method) {
        await this.page.goto(targetRequest.url, {
          waitUntil: 'domcontentloaded',
          timeout: rule.execution.timeout || 30000
        });
      } else {
        // 对于POST/PUT/DELETE请求，使用page.evaluate执行fetch
        await this.page.evaluate(async ({ url, method, headers, body }) => {
          const response = await fetch(url, {
            method,
            headers,
            body: body ? JSON.stringify(body) : undefined
          });
          return response;
        }, {
          url: targetRequest.url,
          method: targetRequest.method,
          headers: targetRequest.headers || {},
          body: requestData
        });
      }
      
      // 等待验证结果
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 获取验证结果
      const validationResult = this.resultCapture.getLatestValidationResult(targetRequest.url);
      
      if (validationResult) {
        return validationResult;
      } else {
        // 创建默认结果
        return {
          url: targetRequest.url,
          method: targetRequest.method || 'GET',
          statusCode: 200,
          responseTime: Date.now() - startTime,
          isValid: true,
          errors: [],
          warnings: [],
          timestamp: new Date()
        };
      }
      
    } catch (error) {
      return {
        url: targetRequest.url,
        method: targetRequest.method || 'GET',
        statusCode: 0,
        responseTime: Date.now() - startTime,
        isValid: false,
        errors: [error instanceof Error ? error.message : String(error)],
        warnings: [],
        timestamp: new Date()
      };
    }
  }

  /**
   * 应用数据映射
   */
  private applyDataMapping(mappings: any[], responseData: any): void {
    for (const mapping of mappings) {
      try {
        const value = this.getValueByPath(responseData, mapping.from);
        this.setVariable(mapping.to, value);
      } catch (error) {
        this.logger.warn('Failed to apply data mapping', { mapping, error });
      }
    }
  }

  /**
   * 根据路径获取值
   */
  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 设置上下文变量
   */
  setVariable(name: string, value: any): void {
    this.context.variables[name] = value;
    this.logger.debug('Set context variable', { name, value });
  }

  /**
   * 获取上下文变量
   */
  getVariable(name: string): any {
    return this.context.variables[name];
  }

  /**
   * 获取执行上下文
   */
  getContext(): RequestOrchestrationContext {
    return { ...this.context };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.rules.clear();
    this.context = {
      executionId: '',
      currentStep: 0,
      totalSteps: 0,
      variables: {},
      results: [],
      errors: []
    };
    this.isExecuting = false;
    this.logger.debug('RequestOrchestrator cleaned up');
  }
}
