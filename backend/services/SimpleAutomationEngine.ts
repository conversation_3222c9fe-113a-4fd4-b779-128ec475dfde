import { v4 as uuidv4 } from 'uuid';
import {
  TestExecution,
  TestStepResult,
  BrowserConfig,
  AIProvider
} from '../../shared/types';
import { Logger } from '../utils/logger';

export class SimpleAutomationEngine {
  private logger: Logger;
  private currentExecution: TestExecution | null = null;

  constructor() {
    this.logger = new Logger('SimpleAutomationEngine');
  }

  /**
   * 初始化自动化引擎
   */
  async initialize(aiProvider?: AIProvider, browserConfig?: BrowserConfig): Promise<void> {
    this.logger.info('Simple automation engine initialized', {
      aiProvider: aiProvider?.name || 'none',
      headless: browserConfig?.headless || false,
    });
  }

  /**
   * 执行自动化测试步骤
   */
  async executeSteps(
    steps: string[], 
    testCaseId?: string,
    options?: {
      saveScreenshots?: boolean;
      generateReport?: boolean;
    }
  ): Promise<TestExecution> {
    const executionId = uuidv4();
    const startTime = new Date();
    
    this.currentExecution = {
      id: executionId,
      testCaseId: testCaseId || '',
      status: 'running',
      startTime,
      results: [],
      screenshots: [],
      logs: [],
    };

    this.logger.info('Starting test execution', { 
      executionId, 
      stepsCount: steps.length,
      testCaseId 
    });

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        const stepId = uuidv4();
        const stepStartTime = new Date();
        
        this.logger.info(`Executing step ${i + 1}/${steps.length}: ${step}`);
        
        const stepResult: TestStepResult = {
          stepId,
          status: 'running',
          startTime: stepStartTime,
          logs: [],
        };
        
        this.currentExecution.results.push(stepResult);

        try {
          // 模拟执行步骤
          await this.simulateStepExecution(step);
          
          stepResult.status = 'passed';
          stepResult.endTime = new Date();
          stepResult.logs.push(`Step completed: ${step}`);
          
          this.logger.info(`Step ${i + 1} completed successfully`);
          
        } catch (error) {
          stepResult.status = 'failed';
          stepResult.endTime = new Date();
          stepResult.error = error instanceof Error ? error.message : String(error);
          stepResult.logs.push(`Step failed: ${error}`);
          
          this.logger.error(`Step ${i + 1} failed`, error);
          
          // 继续执行后续步骤，但标记为失败
          break;
        }
      }
      
      // 检查整体执行状态
      const hasFailedSteps = this.currentExecution.results.some(r => r.status === 'failed');
      this.currentExecution.status = hasFailedSteps ? 'failed' : 'completed';
      this.currentExecution.endTime = new Date();
      
      this.logger.info('Test execution completed', {
        executionId,
        status: this.currentExecution.status,
        duration: this.currentExecution.endTime.getTime() - startTime.getTime(),
      });
      
      return this.currentExecution;
      
    } catch (error) {
      this.currentExecution.status = 'failed';
      this.currentExecution.endTime = new Date();
      this.currentExecution.error = error instanceof Error ? error.message : String(error);
      
      this.logger.error('Test execution failed', error);
      throw error;
    }
  }

  /**
   * 模拟执行单个步骤
   */
  private async simulateStepExecution(step: string): Promise<void> {
    // 模拟执行时间
    const executionTime = Math.random() * 2000 + 500; // 0.5-2.5秒
    await new Promise(resolve => setTimeout(resolve, executionTime));
    
    // 模拟一些步骤可能失败
    const failureRate = 0.1; // 10%失败率
    if (Math.random() < failureRate) {
      throw new Error(`模拟执行失败: ${step}`);
    }
    
    this.logger.debug(`Simulated execution of: ${step}`);
  }

  /**
   * 获取当前执行状态
   */
  getCurrentExecution(): TestExecution | null {
    return this.currentExecution;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.currentExecution = null;
    this.logger.info('Simple automation engine cleaned up');
  }
}
