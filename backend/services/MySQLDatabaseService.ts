import mysql from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import { Logger } from '../utils/logger';
import { createMySQLPool, testMySQLConnection, initializeMySQLTables, MySQLConfig } from '../config/database';

// 网站测试结果接口
export interface WebsiteTestResult {
  id: string;
  websiteUrl: string;
  testType: 'link_check' | 'error_monitor' | 'full_test';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  totalLinks: number;
  successfulLinks: number;
  failedLinks: number;
  serverErrors: number;
  frontendErrors: number;
  createdAt: Date;
  updatedAt: Date;
}

// 链接测试详情接口
export interface LinkTestDetail {
  id: string;
  testResultId: string;
  url: string;
  httpStatus?: number;
  responseTime?: number;
  errorMessage?: string;
  serverError: boolean;
  frontendError: boolean;
  screenshotPath?: string;
  testedAt: Date;
  createdAt: Date;
}

// 错误日志接口
export interface ErrorLog {
  id: string;
  testResultId?: string;
  linkDetailId?: string;
  errorType: 'server_error' | 'frontend_error' | 'network_error' | 'timeout_error';
  errorCode?: string;
  errorMessage: string;
  stackTrace?: string;
  url?: string;
  userAgent?: string;
  createdAt: Date;
}

export class MySQLDatabaseService {
  private pool: mysql.Pool | null = null;
  private logger: Logger;

  constructor(config?: MySQLConfig) {
    this.logger = new Logger('MySQLDatabaseService');
    if (config) {
      this.pool = createMySQLPool(config);
    }
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    try {
      if (!this.pool) {
        this.pool = createMySQLPool();
      }

      // 测试连接
      const isConnected = await testMySQLConnection(this.pool);
      if (!isConnected) {
        throw new Error('Failed to establish MySQL connection');
      }

      // 初始化表结构
      await initializeMySQLTables(this.pool);
      
      this.logger.info('MySQL database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize MySQL database service', error);
      throw error;
    }
  }

  /**
   * 创建网站测试结果记录
   */
  async createWebsiteTestResult(data: {
    websiteUrl: string;
    testType: 'link_check' | 'error_monitor' | 'full_test';
  }): Promise<WebsiteTestResult> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      const id = uuidv4();
      const now = new Date();
      
      const [result] = await this.pool.execute(`
        INSERT INTO website_test_results (
          id, website_url, test_type, status, start_time, 
          total_links, successful_links, failed_links, 
          server_errors, frontend_errors
        ) VALUES (?, ?, ?, 'pending', ?, 0, 0, 0, 0, 0)
      `, [id, data.websiteUrl, data.testType, now]);

      const testResult: WebsiteTestResult = {
        id,
        websiteUrl: data.websiteUrl,
        testType: data.testType,
        status: 'pending',
        startTime: now,
        totalLinks: 0,
        successfulLinks: 0,
        failedLinks: 0,
        serverErrors: 0,
        frontendErrors: 0,
        createdAt: now,
        updatedAt: now
      };

      this.logger.info('Website test result created', { testResultId: id });
      return testResult;
    } catch (error) {
      this.logger.error('Failed to create website test result', error);
      throw error;
    }
  }

  /**
   * 更新网站测试结果
   */
  async updateWebsiteTestResult(
    id: string, 
    updates: Partial<Omit<WebsiteTestResult, 'id' | 'createdAt'>>
  ): Promise<void> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      const setClause = [];
      const values = [];

      // 构建动态更新语句
      if (updates.status !== undefined) {
        setClause.push('status = ?');
        values.push(updates.status);
      }
      if (updates.endTime !== undefined) {
        setClause.push('end_time = ?');
        values.push(updates.endTime);
      }
      if (updates.totalLinks !== undefined) {
        setClause.push('total_links = ?');
        values.push(updates.totalLinks);
      }
      if (updates.successfulLinks !== undefined) {
        setClause.push('successful_links = ?');
        values.push(updates.successfulLinks);
      }
      if (updates.failedLinks !== undefined) {
        setClause.push('failed_links = ?');
        values.push(updates.failedLinks);
      }
      if (updates.serverErrors !== undefined) {
        setClause.push('server_errors = ?');
        values.push(updates.serverErrors);
      }
      if (updates.frontendErrors !== undefined) {
        setClause.push('frontend_errors = ?');
        values.push(updates.frontendErrors);
      }

      if (setClause.length === 0) return;

      values.push(id);
      
      await this.pool.execute(`
        UPDATE website_test_results 
        SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, values);

      this.logger.info('Website test result updated', { testResultId: id });
    } catch (error) {
      this.logger.error('Failed to update website test result', error);
      throw error;
    }
  }

  /**
   * 获取网站测试结果
   */
  async getWebsiteTestResult(id: string): Promise<WebsiteTestResult | null> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      const [rows] = await this.pool.execute(`
        SELECT * FROM website_test_results WHERE id = ?
      `, [id]);

      const results = rows as any[];
      if (results.length === 0) return null;

      return this.mapRowToWebsiteTestResult(results[0]);
    } catch (error) {
      this.logger.error('Failed to get website test result', error);
      throw error;
    }
  }

  /**
   * 创建链接测试详情
   */
  async createLinkTestDetail(data: {
    testResultId: string;
    url: string;
    httpStatus?: number;
    responseTime?: number;
    errorMessage?: string;
    serverError: boolean;
    frontendError: boolean;
    screenshotPath?: string;
  }): Promise<LinkTestDetail> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      const id = uuidv4();
      const now = new Date();
      
      await this.pool.execute(`
        INSERT INTO link_test_details (
          id, test_result_id, url, http_status, response_time,
          error_message, server_error, frontend_error, 
          screenshot_path, tested_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, data.testResultId, data.url, data.httpStatus, data.responseTime,
        data.errorMessage, data.serverError, data.frontendError,
        data.screenshotPath, now
      ]);

      const linkDetail: LinkTestDetail = {
        id,
        testResultId: data.testResultId,
        url: data.url,
        httpStatus: data.httpStatus,
        responseTime: data.responseTime,
        errorMessage: data.errorMessage,
        serverError: data.serverError,
        frontendError: data.frontendError,
        screenshotPath: data.screenshotPath,
        testedAt: now,
        createdAt: now
      };

      this.logger.info('Link test detail created', { linkDetailId: id });
      return linkDetail;
    } catch (error) {
      this.logger.error('Failed to create link test detail', error);
      throw error;
    }
  }

  /**
   * 创建错误日志
   */
  async createErrorLog(data: {
    testResultId?: string;
    linkDetailId?: string;
    errorType: 'server_error' | 'frontend_error' | 'network_error' | 'timeout_error';
    errorCode?: string;
    errorMessage: string;
    stackTrace?: string;
    url?: string;
    userAgent?: string;
  }): Promise<ErrorLog> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      const id = uuidv4();
      const now = new Date();
      
      await this.pool.execute(`
        INSERT INTO error_logs (
          id, test_result_id, link_detail_id, error_type,
          error_code, error_message, stack_trace, url, user_agent
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id, data.testResultId, data.linkDetailId, data.errorType,
        data.errorCode, data.errorMessage, data.stackTrace, 
        data.url, data.userAgent
      ]);

      const errorLog: ErrorLog = {
        id,
        testResultId: data.testResultId,
        linkDetailId: data.linkDetailId,
        errorType: data.errorType,
        errorCode: data.errorCode,
        errorMessage: data.errorMessage,
        stackTrace: data.stackTrace,
        url: data.url,
        userAgent: data.userAgent,
        createdAt: now
      };

      this.logger.info('Error log created', { errorLogId: id });
      return errorLog;
    } catch (error) {
      this.logger.error('Failed to create error log', error);
      throw error;
    }
  }

  /**
   * 获取测试历史
   */
  async getTestHistory(options: {
    page?: number;
    limit?: number;
    websiteUrl?: string;
    testType?: string;
    status?: string;
  } = {}): Promise<WebsiteTestResult[]> {
    if (!this.pool) throw new Error('Database not initialized');

    try {
      const { page = 1, limit = 20, websiteUrl, testType, status } = options;
      const offset = (page - 1) * limit;
      
      let whereClause = '';
      const whereParams = [];
      
      if (websiteUrl) {
        whereClause += ' WHERE website_url LIKE ?';
        whereParams.push(`%${websiteUrl}%`);
      }
      
      if (testType) {
        whereClause += whereClause ? ' AND test_type = ?' : ' WHERE test_type = ?';
        whereParams.push(testType);
      }
      
      if (status) {
        whereClause += whereClause ? ' AND status = ?' : ' WHERE status = ?';
        whereParams.push(status);
      }

      const [rows] = await this.pool.execute(`
        SELECT * FROM website_test_results 
        ${whereClause}
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `, [...whereParams, limit, offset]);

      const results = rows as any[];
      return results.map(row => this.mapRowToWebsiteTestResult(row));
    } catch (error) {
      this.logger.error('Failed to get test history', error);
      throw error;
    }
  }

  /**
   * 映射数据库行到WebsiteTestResult对象
   */
  private mapRowToWebsiteTestResult(row: any): WebsiteTestResult {
    return {
      id: row.id,
      websiteUrl: row.website_url,
      testType: row.test_type,
      status: row.status,
      startTime: new Date(row.start_time),
      endTime: row.end_time ? new Date(row.end_time) : undefined,
      totalLinks: row.total_links,
      successfulLinks: row.successful_links,
      failedLinks: row.failed_links,
      serverErrors: row.server_errors,
      frontendErrors: row.frontend_errors,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      this.logger.info('MySQL database connection closed');
    }
  }
}
