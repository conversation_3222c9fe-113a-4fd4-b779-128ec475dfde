import { Stagehand } from '@browserbasehq/stagehand';
import { URL } from 'url';
import { Logger } from '../utils/logger';
import { DiscoveredLink, WebsiteSitemap } from '@shared/types/websiteTest';

export interface LinkDiscoveryConfig {
  maxDepth: number;
  maxPages: number;
  followExternalLinks: boolean;
  respectRobotsTxt: boolean;
  delay: number;
  concurrent: number;
  timeout: number;
  userAgent?: string;
}

export class LinkDiscoveryService {
  private logger: Logger;
  private stagehand: Stagehand | null = null;
  private visitedUrls: Set<string> = new Set();
  private discoveredLinks: DiscoveredLink[] = [];
  private config: LinkDiscoveryConfig;

  constructor(config: Partial<LinkDiscoveryConfig> = {}) {
    this.logger = new Logger('LinkDiscoveryService');
    this.config = {
      maxDepth: config.maxDepth || 3,
      maxPages: config.maxPages || 100,
      followExternalLinks: config.followExternalLinks || false,
      respectRobotsTxt: config.respectRobotsTxt || true,
      delay: config.delay || 1000,
      concurrent: config.concurrent || 3,
      timeout: config.timeout || 30000,
      userAgent: config.userAgent
    };
  }

  /**
   * 初始化浏览器
   */
  async initialize(): Promise<void> {
    try {
      this.stagehand = new Stagehand({
        env: 'LOCAL'
      });
      
      await this.stagehand.init();
      this.logger.info('Link discovery service initialized');
    } catch (error) {
      this.logger.error('Failed to initialize link discovery service', error);
      throw error;
    }
  }

  /**
   * 发现网站所有链接
   */
  async discoverLinks(baseUrl: string): Promise<WebsiteSitemap> {
    if (!this.stagehand) {
      throw new Error('Service not initialized');
    }

    this.logger.info('Starting link discovery', { baseUrl });
    
    try {
      // 重置状态
      this.visitedUrls.clear();
      this.discoveredLinks = [];

      const startTime = Date.now();
      const errors: string[] = [];

      // 检查robots.txt
      if (this.config.respectRobotsTxt) {
        await this.checkRobotsTxt(baseUrl);
      }

      // 开始递归爬取
      await this.crawlPage(baseUrl, 0, baseUrl);

      const sitemap: WebsiteSitemap = {
        baseUrl,
        totalPages: this.visitedUrls.size,
        internalLinks: this.discoveredLinks.filter(link => link.type === 'internal'),
        externalLinks: this.discoveredLinks.filter(link => link.type === 'external'),
        resources: this.discoveredLinks.filter(link => link.type === 'resource'),
        errors,
        generatedAt: new Date()
      };

      this.logger.info('Link discovery completed', {
        baseUrl,
        totalPages: sitemap.totalPages,
        internalLinks: sitemap.internalLinks.length,
        externalLinks: sitemap.externalLinks.length,
        resources: sitemap.resources.length,
        duration: Date.now() - startTime
      });

      return sitemap;
    } catch (error) {
      this.logger.error('Link discovery failed', error);
      throw error;
    }
  }

  /**
   * 爬取单个页面
   */
  private async crawlPage(url: string, depth: number, sourceUrl: string): Promise<void> {
    // 检查深度限制
    if (depth > this.config.maxDepth) {
      return;
    }

    // 检查页面数量限制
    if (this.visitedUrls.size >= this.config.maxPages) {
      return;
    }

    // 检查是否已访问
    if (this.visitedUrls.has(url)) {
      return;
    }

    this.visitedUrls.add(url);
    this.logger.debug('Crawling page', { url, depth });

    try {
      if (!this.stagehand) return;

      // 导航到页面
      await this.stagehand.page.goto(url, {
        waitUntil: 'networkidle',
        timeout: this.config.timeout
      });

      // 等待页面加载
      await this.stagehand.page.waitForTimeout(this.config.delay);

      // 提取所有链接
      const links = await this.extractLinksFromPage(url, depth, sourceUrl);
      
      // 添加到发现的链接列表
      this.discoveredLinks.push(...links);

      // 递归爬取内部链接
      const internalLinks = links.filter(link => 
        link.type === 'internal' && 
        !this.visitedUrls.has(link.url)
      );

      for (const link of internalLinks) {
        await this.crawlPage(link.url, depth + 1, url);
      }

    } catch (error: any) {
      this.logger.warn('Failed to crawl page', { url, error: error.message });
    }
  }

  /**
   * 从页面提取链接
   */
  private async extractLinksFromPage(
    currentUrl: string, 
    depth: number, 
    sourceUrl: string
  ): Promise<DiscoveredLink[]> {
    if (!this.stagehand) return [];

    try {
      const links = await this.stagehand.page.evaluate((currentUrl) => {
        const baseUrl = new URL(currentUrl);
        const discoveredLinks: any[] = [];

        // 提取所有a标签链接
        const anchorElements = document.querySelectorAll('a[href]');
        anchorElements.forEach((element: any, index: number) => {
          const href = element.getAttribute('href');
          if (!href) return;

          try {
            const absoluteUrl = new URL(href, currentUrl).href;
            const linkUrl = new URL(absoluteUrl);
            
            discoveredLinks.push({
              url: absoluteUrl,
              text: element.textContent?.trim() || '',
              title: element.getAttribute('title') || '',
              type: linkUrl.hostname === baseUrl.hostname ? 'internal' : 'external',
              selector: `a:nth-child(${index + 1})`,
              attributes: {
                href: href,
                target: element.getAttribute('target') || '',
                rel: element.getAttribute('rel') || ''
              }
            });
          } catch (e) {
            // 忽略无效URL
          }
        });

        // 提取资源链接（图片、CSS、JS等）
        const resourceSelectors = [
          'img[src]',
          'link[href]',
          'script[src]',
          'iframe[src]',
          'video[src]',
          'audio[src]'
        ];

        resourceSelectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach((element: any, index: number) => {
            const src = element.getAttribute('src') || element.getAttribute('href');
            if (!src) return;

            try {
              const absoluteUrl = new URL(src, currentUrl).href;
              const resourceUrl = new URL(absoluteUrl);
              
              discoveredLinks.push({
                url: absoluteUrl,
                text: element.getAttribute('alt') || element.getAttribute('title') || '',
                title: element.getAttribute('title') || '',
                type: 'resource',
                selector: `${selector.split('[')[0]}:nth-child(${index + 1})`,
                attributes: {
                  type: element.tagName.toLowerCase(),
                  src: src
                }
              });
            } catch (e) {
              // 忽略无效URL
            }
          });
        });

        return discoveredLinks;
      }, currentUrl);

      // 处理提取的链接
      return links.map(link => ({
        ...link,
        depth,
        sourceUrl,
        type: this.determineUrlType(link.url, currentUrl)
      }));

    } catch (error) {
      this.logger.error('Failed to extract links from page', { currentUrl, error });
      return [];
    }
  }

  /**
   * 确定URL类型
   */
  private determineUrlType(url: string, baseUrl: string): 'internal' | 'external' | 'anchor' | 'resource' {
    try {
      const urlObj = new URL(url);
      const baseUrlObj = new URL(baseUrl);

      // 锚点链接
      if (url.startsWith('#')) {
        return 'anchor';
      }

      // 资源文件
      const resourceExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.css', '.js', '.pdf', '.doc', '.docx'];
      const pathname = urlObj.pathname.toLowerCase();
      if (resourceExtensions.some(ext => pathname.endsWith(ext))) {
        return 'resource';
      }

      // 内部或外部链接
      return urlObj.hostname === baseUrlObj.hostname ? 'internal' : 'external';
    } catch (error) {
      return 'external';
    }
  }

  /**
   * 检查robots.txt
   */
  private async checkRobotsTxt(baseUrl: string): Promise<void> {
    try {
      const robotsUrl = new URL('/robots.txt', baseUrl).href;
      
      if (!this.stagehand) return;
      
      const response = await this.stagehand.page.goto(robotsUrl, {
        waitUntil: 'networkidle',
        timeout: 10000
      });
      
      if (response?.ok()) {
        const robotsContent = await this.stagehand.page.content();
        this.logger.info('Robots.txt found and will be respected', { baseUrl });
        // 这里可以解析robots.txt内容并应用规则
      }
    } catch (error) {
      this.logger.debug('No robots.txt found or failed to load', { baseUrl });
    }
  }

  /**
   * 获取页面标题
   */
  async getPageTitle(url: string): Promise<string> {
    if (!this.stagehand) return '';

    try {
      await this.stagehand.page.goto(url, { 
        waitUntil: 'domcontentloaded',
        timeout: this.config.timeout 
      });
      
      const title = await this.stagehand.page.title();
      return title || '';
    } catch (error: any) {
      this.logger.warn('Failed to get page title', { url, error: error.message });
      return '';
    }
  }

  /**
   * 检查URL是否可访问
   */
  async checkUrlAccessibility(url: string): Promise<{
    accessible: boolean;
    status?: number;
    error?: string;
  }> {
    if (!this.stagehand) {
      return { accessible: false, error: 'Service not initialized' };
    }

    try {
      const response = await this.stagehand.page.goto(url, { 
        waitUntil: 'domcontentloaded',
        timeout: this.config.timeout 
      });
      
      if (response) {
        return {
          accessible: response.ok(),
          status: response.status()
        };
      }
      
      return { accessible: false, error: 'No response received' };
    } catch (error: any) {
      return {
        accessible: false,
        error: error.message
      };
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    if (this.stagehand) {
      await this.stagehand.close();
      this.stagehand = null;
    }
    this.visitedUrls.clear();
    this.discoveredLinks = [];
    this.logger.info('Link discovery service cleaned up');
  }
}
