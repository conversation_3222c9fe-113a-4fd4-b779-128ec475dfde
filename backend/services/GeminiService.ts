import axios from 'axios';

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

export class GeminiService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY || '';
    this.baseUrl = 'https://openrouter.ai/api/v1';
  }

  /**
   * 使用Gemini分析和优化测试步骤
   */
  async analyzeTestSteps(steps: string[]): Promise<{
    optimizedSteps: string[];
    suggestions: string[];
    confidence: number;
  }> {
    if (!this.apiKey) {
      // 如果没有API密钥，返回原始步骤
      return {
        optimizedSteps: steps,
        suggestions: ['请配置OPENROUTER_API_KEY以启用AI优化功能'],
        confidence: 0.5
      };
    }

    try {
      const prompt = this.buildAnalysisPrompt(steps);
      const response = await this.callGeminiAPI(prompt);
      
      return this.parseAnalysisResponse(response, steps);
    } catch (error) {
      console.error('Gemini API调用失败:', error);
      return {
        optimizedSteps: steps,
        suggestions: ['AI分析暂时不可用，使用原始步骤'],
        confidence: 0.5
      };
    }
  }

  /**
   * 将自然语言转换为结构化的测试步骤
   */
  async parseNaturalLanguage(description: string): Promise<{
    steps: string[];
    actionType: string;
    confidence: number;
  }> {
    if (!this.apiKey) {
      return {
        steps: [description],
        actionType: 'custom',
        confidence: 0.5
      };
    }

    try {
      const prompt = this.buildParsingPrompt(description);
      const response = await this.callGeminiAPI(prompt);
      
      return this.parseNLResponse(response, description);
    } catch (error) {
      console.error('自然语言解析失败:', error);
      return {
        steps: [description],
        actionType: 'custom',
        confidence: 0.5
      };
    }
  }

  /**
   * 调用Gemini API
   */
  private async callGeminiAPI(prompt: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: 'google/gemini-2.0-flash-exp:free',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1000,
          temperature: 0.3
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:3001',
            'X-Title': 'AI Browser Automation Agent'
          },
          timeout: 30000
        }
      );

      return response.data.choices[0].message.content;
    } catch (error: any) {
      // 记录详细的错误信息
      console.error('OpenRouter API调用详细错误:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
      throw error;
    }
  }

  /**
   * 构建分析提示词
   */
  private buildAnalysisPrompt(steps: string[]): string {
    return `你是一个专业的浏览器自动化测试专家。请分析以下测试步骤，并提供优化建议：

测试步骤：
${steps.map((step, index) => `${index + 1}. ${step}`).join('\n')}

请按以下格式返回分析结果：

OPTIMIZED_STEPS:
[优化后的步骤列表，每行一个步骤]

SUGGESTIONS:
[改进建议列表，每行一个建议]

CONFIDENCE:
[置信度分数，0-1之间的数字]

要求：
1. 优化步骤的描述，使其更清晰、更具体
2. 确保步骤的逻辑顺序正确
3. 添加必要的等待和验证步骤
4. 提供实用的改进建议
5. 保持步骤的可执行性`;
  }

  /**
   * 构建自然语言解析提示词
   */
  private buildParsingPrompt(description: string): string {
    return `你是一个浏览器自动化测试专家。请将以下自然语言描述转换为具体的测试步骤：

描述：${description}

请按以下格式返回结果：

STEPS:
[具体的测试步骤列表，每行一个步骤]

ACTION_TYPE:
[主要动作类型：navigate, click, type, verify, wait, scroll, hover, custom]

CONFIDENCE:
[置信度分数，0-1之间的数字]

要求：
1. 将描述分解为具体的、可执行的步骤
2. 使用清晰的动作词汇（如：打开、点击、输入、验证等）
3. 包含必要的等待和验证步骤
4. 确保步骤的逻辑顺序`;
  }

  /**
   * 解析分析响应
   */
  private parseAnalysisResponse(response: string, originalSteps: string[]): {
    optimizedSteps: string[];
    suggestions: string[];
    confidence: number;
  } {
    try {
      const optimizedStepsMatch = response.match(/OPTIMIZED_STEPS:\s*([\s\S]*?)(?=SUGGESTIONS:|$)/);
      const suggestionsMatch = response.match(/SUGGESTIONS:\s*([\s\S]*?)(?=CONFIDENCE:|$)/);
      const confidenceMatch = response.match(/CONFIDENCE:\s*([\d.]+)/);

      const optimizedSteps = optimizedStepsMatch 
        ? optimizedStepsMatch[1].trim().split('\n').map(s => s.replace(/^\d+\.\s*/, '').trim()).filter(s => s)
        : originalSteps;

      const suggestions = suggestionsMatch
        ? suggestionsMatch[1].trim().split('\n').map(s => s.replace(/^[-*]\s*/, '').trim()).filter(s => s)
        : ['AI分析完成'];

      const confidence = confidenceMatch 
        ? Math.min(Math.max(parseFloat(confidenceMatch[1]), 0), 1)
        : 0.7;

      return {
        optimizedSteps,
        suggestions,
        confidence
      };
    } catch (error) {
      console.error('解析AI响应失败:', error);
      return {
        optimizedSteps: originalSteps,
        suggestions: ['AI响应解析失败'],
        confidence: 0.5
      };
    }
  }

  /**
   * 解析自然语言响应
   */
  private parseNLResponse(response: string, originalDescription: string): {
    steps: string[];
    actionType: string;
    confidence: number;
  } {
    try {
      const stepsMatch = response.match(/STEPS:\s*([\s\S]*?)(?=ACTION_TYPE:|$)/);
      const actionTypeMatch = response.match(/ACTION_TYPE:\s*(\w+)/);
      const confidenceMatch = response.match(/CONFIDENCE:\s*([\d.]+)/);

      const steps = stepsMatch
        ? stepsMatch[1].trim().split('\n').map(s => s.replace(/^\d+\.\s*/, '').trim()).filter(s => s)
        : [originalDescription];

      const actionType = actionTypeMatch 
        ? actionTypeMatch[1].toLowerCase()
        : 'custom';

      const confidence = confidenceMatch
        ? Math.min(Math.max(parseFloat(confidenceMatch[1]), 0), 1)
        : 0.7;

      return {
        steps,
        actionType,
        confidence
      };
    } catch (error) {
      console.error('解析自然语言响应失败:', error);
      return {
        steps: [originalDescription],
        actionType: 'custom',
        confidence: 0.5
      };
    }
  }
}
