import { TestExecution, TestCase } from '@shared/types';
import { Logger } from '../utils/logger';

export class MemoryDatabase {
  private executions: Map<string, TestExecution> = new Map();
  private testCases: Map<string, TestCase> = new Map();
  private logger: Logger;

  constructor() {
    this.logger = new Logger('MemoryDatabase');
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    this.logger.info('Memory database initialized');
  }

  /**
   * 保存测试执行记录
   */
  async saveExecution(execution: TestExecution): Promise<void> {
    this.executions.set(execution.id, execution);
    this.logger.info('Execution saved', { executionId: execution.id });
  }

  /**
   * 获取测试执行记录
   */
  async getExecution(executionId: string): Promise<TestExecution | null> {
    return this.executions.get(executionId) || null;
  }

  /**
   * 获取测试执行历史
   */
  async getExecutions(options: {
    page?: number;
    limit?: number;
    status?: string;
  } = {}): Promise<TestExecution[]> {
    const { page = 1, limit = 20, status } = options;
    
    let executions = Array.from(this.executions.values());
    
    // 状态过滤
    if (status) {
      executions = executions.filter(exec => exec.status === status);
    }
    
    // 按开始时间倒序排序
    executions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
    
    // 分页
    const start = (page - 1) * limit;
    const end = start + limit;
    
    return executions.slice(start, end);
  }

  /**
   * 保存测试用例
   */
  async saveTestCase(testCase: TestCase): Promise<void> {
    this.testCases.set(testCase.id, testCase);
    this.logger.info('Test case saved', { testCaseId: testCase.id });
  }

  /**
   * 获取测试用例
   */
  async getTestCase(testCaseId: string): Promise<TestCase | null> {
    return this.testCases.get(testCaseId) || null;
  }

  /**
   * 获取所有测试用例
   */
  async getTestCases(): Promise<TestCase[]> {
    const testCases = Array.from(this.testCases.values());
    return testCases.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  /**
   * 删除测试用例
   */
  async deleteTestCase(testCaseId: string): Promise<void> {
    this.testCases.delete(testCaseId);
    this.logger.info('Test case deleted', { testCaseId });
  }

  /**
   * 获取执行统计信息
   */
  async getExecutionStats(): Promise<{
    total: number;
    completed: number;
    failed: number;
    running: number;
  }> {
    const executions = Array.from(this.executions.values());
    
    return {
      total: executions.length,
      completed: executions.filter(e => e.status === 'completed').length,
      failed: executions.filter(e => e.status === 'failed').length,
      running: executions.filter(e => e.status === 'running').length,
    };
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    this.executions.clear();
    this.testCases.clear();
    this.logger.info('Memory database closed');
  }
}
