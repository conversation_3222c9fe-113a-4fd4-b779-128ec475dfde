import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { 
  TestExecution, 
  TestStepResult, 
  BrowserConfig, 
  AIProvider,
  AutomationError,
  ActionType 
} from '@shared/types';
import { config, getAIProviderConfig, getBrowserbaseConfig } from '../config';
import { Logger } from '../utils/logger';

export class AutomationEngine {
  private stagehand: Stagehand | null = null;
  private logger: Logger;
  private currentExecution: TestExecution | null = null;

  constructor() {
    this.logger = new Logger('AutomationEngine');
  }

  /**
   * 初始化Stagehand实例
   */
  async initialize(aiProvider?: AIProvider, browserConfig?: BrowserConfig): Promise<void> {
    try {
      this.logger.info('Initializing automation engine...');
      
      // 获取AI提供商配置
      const aiConfigs = getAIProviderConfig();
      const selectedProvider = aiProvider || this.getDefaultAIProvider(aiConfigs);
      
      if (!selectedProvider) {
        throw new Error('No AI provider configured. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY');
      }

      // 获取浏览器配置
      const finalBrowserConfig = {
        ...config.browser,
        ...browserConfig,
      };

      // Browserbase配置（如果可用）
      const browserbaseConfig = getBrowserbaseConfig();

      // 初始化Stagehand
      const stagehandConfig: any = {
        env: process.env.NODE_ENV || 'development',
        apiKey: selectedProvider.apiKey,
        modelName: selectedProvider.model,
        headless: finalBrowserConfig.headless,
        browserbaseAPIKey: browserbaseConfig?.apiKey,
        browserbaseProjectId: browserbaseConfig?.projectId,
        viewport: finalBrowserConfig.viewport,
        defaultTimeout: finalBrowserConfig.timeout,
      };

      this.stagehand = new Stagehand(stagehandConfig);
      await this.stagehand.init();
      
      this.logger.info('Automation engine initialized successfully', {
        provider: selectedProvider.name,
        model: selectedProvider.model,
        headless: finalBrowserConfig.headless,
      });
    } catch (error) {
      this.logger.error('Failed to initialize automation engine', error);
      throw error;
    }
  }

  /**
   * 执行自动化测试步骤
   */
  async executeSteps(
    steps: string[],
    testCaseId?: string,
    options?: {
      saveScreenshots?: boolean;
      generateReport?: boolean;
      requestValidation?: any;
      requestOrchestration?: any;
    }
  ): Promise<TestExecution> {
    if (!this.stagehand) {
      throw new Error('Automation engine not initialized. Call initialize() first.');
    }

    const executionId = uuidv4();
    const startTime = new Date();
    
    this.currentExecution = {
      id: executionId,
      testCaseId: testCaseId || '',
      status: 'running',
      startTime,
      results: [],
      screenshots: [],
      logs: [],
    };

    this.logger.info('Starting test execution', { 
      executionId, 
      stepsCount: steps.length,
      testCaseId 
    });

    try {
      const page = this.stagehand.page;
      
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        const stepId = uuidv4();
        const stepStartTime = new Date();
        
        this.logger.info(`Executing step ${i + 1}/${steps.length}: ${step}`);
        
        const stepResult: TestStepResult = {
          stepId,
          status: 'running',
          startTime: stepStartTime,
          logs: [],
        };
        
        this.currentExecution.results.push(stepResult);

        try {
          // 执行步骤
          await this.executeStep(step, page);
          
          // 截图（如果启用）
          if (options?.saveScreenshots) {
            const screenshotPath = await this.takeScreenshot(executionId, stepId);
            if (screenshotPath) {
              stepResult.screenshot = screenshotPath;
              this.currentExecution.screenshots.push(screenshotPath);
            }
          }
          
          stepResult.status = 'passed';
          stepResult.endTime = new Date();
          
          this.logger.info(`Step ${i + 1} completed successfully`);
          
        } catch (error) {
          stepResult.status = 'failed';
          stepResult.endTime = new Date();
          stepResult.error = error instanceof Error ? error.message : String(error);
          
          this.logger.error(`Step ${i + 1} failed`, error);
          
          // 失败时也截图
          if (options?.saveScreenshots) {
            const screenshotPath = await this.takeScreenshot(executionId, stepId, 'error');
            if (screenshotPath) {
              stepResult.screenshot = screenshotPath;
              this.currentExecution.screenshots.push(screenshotPath);
            }
          }
          
          // 决定是否继续执行后续步骤
          break;
        }
      }
      
      // 检查整体执行状态
      const hasFailedSteps = this.currentExecution.results.some(r => r.status === 'failed');
      this.currentExecution.status = hasFailedSteps ? 'failed' : 'completed';
      this.currentExecution.endTime = new Date();
      
      this.logger.info('Test execution completed', {
        executionId,
        status: this.currentExecution.status,
        duration: this.currentExecution.endTime.getTime() - startTime.getTime(),
      });
      
      return this.currentExecution;
      
    } catch (error) {
      this.currentExecution.status = 'failed';
      this.currentExecution.endTime = new Date();
      this.currentExecution.error = error instanceof Error ? error.message : String(error);
      
      this.logger.error('Test execution failed', error);
      throw error;
    }
  }

  /**
   * 执行单个步骤
   */
  private async executeStep(step: string, page: any): Promise<void> {
    // 使用Stagehand的act方法执行自然语言指令
    await page.act(step);
  }

  /**
   * 截图
   */
  private async takeScreenshot(
    executionId: string, 
    stepId: string, 
    type: 'normal' | 'error' = 'normal'
  ): Promise<string | null> {
    try {
      if (!this.stagehand?.page) return null;
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${executionId}_${stepId}_${type}_${timestamp}.png`;
      const filepath = `${config.storage.screenshotDir}/${filename}`;
      
      await this.stagehand.page.screenshot({ path: filepath });
      
      return filepath;
    } catch (error) {
      this.logger.error('Failed to take screenshot', error);
      return null;
    }
  }

  /**
   * 获取默认AI提供商
   */
  private getDefaultAIProvider(aiConfigs: ReturnType<typeof getAIProviderConfig>): AIProvider | null {
    if (config.ai.defaultProvider === 'openai' && aiConfigs.openai) {
      return aiConfigs.openai;
    }
    if (config.ai.defaultProvider === 'anthropic' && aiConfigs.anthropic) {
      return aiConfigs.anthropic;
    }
    
    // 回退到任何可用的提供商
    return aiConfigs.openai || aiConfigs.anthropic || null;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    try {
      if (this.stagehand) {
        await this.stagehand.close();
        this.stagehand = null;
      }
      this.logger.info('Automation engine cleaned up');
    } catch (error) {
      this.logger.error('Error during cleanup', error);
    }
  }

  /**
   * 获取当前执行状态
   */
  getCurrentExecution(): TestExecution | null {
    return this.currentExecution;
  }
}
