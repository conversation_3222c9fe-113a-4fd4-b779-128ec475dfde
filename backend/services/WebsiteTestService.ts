import { v4 as uuidv4 } from 'uuid';
import { Logger } from '../utils/logger';
import { MySQLDatabaseService } from './MySQLDatabaseService';
import { LinkDiscoveryService } from './LinkDiscoveryService';
import { ErrorMonitoringService } from './ErrorMonitoringService';
import {
  WebsiteTestConfig,
  WebsiteTestResult,
  TestStatus,
  LinkTestDetail,
  ErrorLog,
  TestSummary,
  WebsiteSitemap
} from '@shared/types/websiteTest';
import { PageErrorReport } from './ErrorMonitoringService';

export class WebsiteTestService {
  private logger: Logger;
  private dbService: MySQLDatabaseService;
  private linkDiscovery: LinkDiscoveryService;
  private errorMonitoring: ErrorMonitoringService;

  constructor(dbService: MySQLDatabaseService) {
    this.logger = new Logger('WebsiteTestService');
    this.dbService = dbService;
    this.linkDiscovery = new LinkDiscoveryService();
    this.errorMonitoring = new ErrorMonitoringService();
  }

  /**
   * 初始化网站测试服务
   */
  async initialize(): Promise<void> {
    try {
      await this.linkDiscovery.initialize();
      await this.errorMonitoring.initialize();
      this.logger.info('Website test service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize website test service', error);
      throw error;
    }
  }

  /**
   * 执行网站测试
   */
  async executeTest(config: WebsiteTestConfig): Promise<WebsiteTestResult> {
    this.logger.info('Starting website test execution', { 
      websiteUrl: config.websiteUrl, 
      testType: config.testType 
    });

    // 创建测试结果记录
    const testResult = await this.dbService.createWebsiteTestResult({
      websiteUrl: config.websiteUrl,
      testType: config.testType
    });

    try {
      // 更新状态为运行中
      await this.dbService.updateWebsiteTestResult(testResult.id, {
        status: 'running',
        startTime: new Date()
      });

      let sitemap: WebsiteSitemap | null = null;
      let linkReports: PageErrorReport[] = [];

      // 根据测试类型执行不同的测试
      switch (config.testType) {
        case 'link_check':
          linkReports = await this.executeLinkCheck(config);
          break;
        case 'error_monitor':
          linkReports = await this.executeErrorMonitoring(config);
          break;
        case 'full_test':
          sitemap = await this.executeFullTest(config);
          linkReports = await this.testDiscoveredLinks(sitemap);
          break;
      }

      // 处理测试结果
      await this.processTestResults(testResult.id, linkReports);

      // 生成测试摘要
      const summary = this.generateTestSummary(linkReports);

      // 更新测试结果
      const finalResult = await this.updateTestResult(testResult.id, linkReports, summary);

      this.logger.info('Website test execution completed', {
        testResultId: testResult.id,
        websiteUrl: config.websiteUrl,
        totalLinks: linkReports.length,
        errors: linkReports.filter(r => r.serverError || r.frontendError).length
      });

      return finalResult;

    } catch (error) {
      this.logger.error('Website test execution failed', { 
        testResultId: testResult.id, 
        error: (error as Error).message
      });

      // 更新状态为失败
      await this.dbService.updateWebsiteTestResult(testResult.id, {
        status: 'failed',
        endTime: new Date()
      });

      // 记录错误日志
      await this.dbService.createErrorLog({
        testResultId: testResult.id,
        errorType: 'server_error',
        errorMessage: (error as Error).message,
        stackTrace: (error as Error).stack,
        url: config.websiteUrl
      });

      throw error;
    }
  }

  /**
   * 执行链接检查
   */
  private async executeLinkCheck(config: WebsiteTestConfig): Promise<PageErrorReport[]> {
    this.logger.info('Executing link check', { websiteUrl: config.websiteUrl });

    // 发现所有链接
    const sitemap = await this.linkDiscovery.discoverLinks(config.websiteUrl);
    
    // 测试所有内部链接
    const linksToTest = sitemap.internalLinks.map(link => link.url);
    
    // 添加基础URL
    if (!linksToTest.includes(config.websiteUrl)) {
      linksToTest.unshift(config.websiteUrl);
    }

    return await this.errorMonitoring.monitorMultiplePages(linksToTest);
  }

  /**
   * 执行错误监控
   */
  private async executeErrorMonitoring(config: WebsiteTestConfig): Promise<PageErrorReport[]> {
    this.logger.info('Executing error monitoring', { websiteUrl: config.websiteUrl });

    // 只监控指定的URL
    const report = await this.errorMonitoring.monitorPage(config.websiteUrl);
    return [report];
  }

  /**
   * 执行完整测试
   */
  private async executeFullTest(config: WebsiteTestConfig): Promise<WebsiteSitemap> {
    this.logger.info('Executing full test', { websiteUrl: config.websiteUrl });

    // 配置链接发现服务
    if (config.crawlerConfig) {
      this.linkDiscovery = new LinkDiscoveryService({
        maxDepth: config.crawlerConfig.maxDepth,
        maxPages: config.crawlerConfig.maxPages,
        followExternalLinks: config.crawlerConfig.followExternalLinks,
        respectRobotsTxt: config.crawlerConfig.respectRobotsTxt,
        delay: config.crawlerConfig.delay,
        concurrent: config.crawlerConfig.concurrent
      });
      await this.linkDiscovery.initialize();
    }

    // 发现网站结构
    return await this.linkDiscovery.discoverLinks(config.websiteUrl);
  }

  /**
   * 测试发现的链接
   */
  private async testDiscoveredLinks(sitemap: WebsiteSitemap): Promise<PageErrorReport[]> {
    const linksToTest = [
      sitemap.baseUrl,
      ...sitemap.internalLinks.map(link => link.url)
    ];

    // 去重
    const uniqueLinks = [...new Set(linksToTest)];
    
    this.logger.info('Testing discovered links', { count: uniqueLinks.length });

    return await this.errorMonitoring.monitorMultiplePages(uniqueLinks);
  }

  /**
   * 处理测试结果
   */
  private async processTestResults(testResultId: string, reports: PageErrorReport[]): Promise<void> {
    for (const report of reports) {
      // 创建链接测试详情
      const linkDetail = await this.dbService.createLinkTestDetail({
        testResultId,
        url: report.url,
        httpStatus: report.httpStatus,
        responseTime: report.responseTime,
        errorMessage: report.errorMessage,
        serverError: report.serverError,
        frontendError: report.frontendError,
        screenshotPath: report.screenshotPath
      });

      // 记录控制台错误
      for (const consoleError of report.consoleErrors) {
        await this.dbService.createErrorLog({
          testResultId,
          linkDetailId: linkDetail.id,
          errorType: 'frontend_error',
          errorMessage: consoleError.message,
          stackTrace: `${consoleError.source}:${consoleError.line}:${consoleError.column}`,
          url: report.url
        });
      }

      // 记录网络错误
      for (const networkError of report.networkErrors) {
        await this.dbService.createErrorLog({
          testResultId,
          linkDetailId: linkDetail.id,
          errorType: 'network_error',
          errorCode: networkError.status?.toString(),
          errorMessage: networkError.errorMessage,
          url: networkError.url
        });
      }

      // 记录服务端错误
      if (report.serverError && report.httpStatus) {
        await this.dbService.createErrorLog({
          testResultId,
          linkDetailId: linkDetail.id,
          errorType: 'server_error',
          errorCode: report.httpStatus.toString(),
          errorMessage: `HTTP ${report.httpStatus} Server Error`,
          url: report.url
        });
      }
    }
  }

  /**
   * 生成测试摘要
   */
  private generateTestSummary(reports: PageErrorReport[]): TestSummary {
    const totalReports = reports.length;
    const successfulReports = reports.filter(r => !r.serverError && !r.frontendError);
    const failedReports = reports.filter(r => r.serverError || r.frontendError);

    const responseTimes = reports.map(r => r.responseTime).filter(t => t > 0);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    const slowestPage = reports.reduce((prev, current) => 
      (current.responseTime > prev.responseTime) ? current : prev
    );

    const fastestPage = reports.reduce((prev, current) => 
      (current.responseTime < prev.responseTime && current.responseTime > 0) ? current : prev
    );

    // 错误类型统计
    const errorBreakdown = {
      server_error: reports.filter(r => r.serverError).length,
      frontend_error: reports.filter(r => r.frontendError).length,
      network_error: reports.reduce((sum, r) => sum + r.networkErrors.length, 0),
      timeout_error: reports.filter(r => r.responseTime > 30000).length
    };

    // HTTP状态码统计
    const statusCodeBreakdown: { [key: string]: number } = {};
    reports.forEach(report => {
      if (report.httpStatus) {
        const status = report.httpStatus.toString();
        statusCodeBreakdown[status] = (statusCodeBreakdown[status] || 0) + 1;
      }
    });

    return {
      duration: 0, // 将在updateTestResult中计算
      averageResponseTime,
      slowestPage: {
        url: slowestPage.url,
        responseTime: slowestPage.responseTime
      },
      fastestPage: {
        url: fastestPage.url,
        responseTime: fastestPage.responseTime
      },
      errorBreakdown,
      statusCodeBreakdown
    };
  }

  /**
   * 更新测试结果
   */
  private async updateTestResult(
    testResultId: string, 
    reports: PageErrorReport[], 
    summary: TestSummary
  ): Promise<WebsiteTestResult> {
    const totalLinks = reports.length;
    const successfulLinks = reports.filter(r => !r.serverError && !r.frontendError).length;
    const failedLinks = totalLinks - successfulLinks;
    const serverErrors = reports.filter(r => r.serverError).length;
    const frontendErrors = reports.filter(r => r.frontendError).length;

    await this.dbService.updateWebsiteTestResult(testResultId, {
      status: 'completed',
      endTime: new Date(),
      totalLinks,
      successfulLinks,
      failedLinks,
      serverErrors,
      frontendErrors
    });

    const result = await this.dbService.getWebsiteTestResult(testResultId);
    if (!result) {
      throw new Error('Failed to retrieve updated test result');
    }

    // 计算测试持续时间
    if (result.endTime && result.startTime) {
      summary.duration = result.endTime.getTime() - result.startTime.getTime();
    }

    return {
      ...result,
      summary
    };
  }

  /**
   * 获取测试结果
   */
  async getTestResult(testResultId: string): Promise<WebsiteTestResult | null> {
    return await this.dbService.getWebsiteTestResult(testResultId);
  }

  /**
   * 获取测试历史
   */
  async getTestHistory(options: {
    page?: number;
    limit?: number;
    websiteUrl?: string;
    testType?: string;
    status?: string;
  } = {}): Promise<WebsiteTestResult[]> {
    return await this.dbService.getTestHistory(options);
  }

  /**
   * 停止正在运行的测试
   */
  async stopTest(testResultId: string): Promise<void> {
    await this.dbService.updateWebsiteTestResult(testResultId, {
      status: 'failed',
      endTime: new Date()
    });

    this.logger.info('Test stopped', { testResultId });
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.linkDiscovery.cleanup();
    await this.errorMonitoring.cleanup();
    this.logger.info('Website test service cleaned up');
  }
}
