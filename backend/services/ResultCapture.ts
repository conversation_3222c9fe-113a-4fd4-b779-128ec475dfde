import { Page } from 'playwright';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import {
  TestExecution,
  TestStepResult,
  RequestValidationRule,
  RequestValidationResult,
  NetworkRequest,
  NetworkResponse
} from '@shared/types';
import { Logger } from '../utils/logger';
import { config } from '../config';
import { ensureDirectoryExists } from '../utils/fileSystem';

export interface CaptureOptions {
  includeScreenshot?: boolean;
  includePageSource?: boolean;
  includeConsoleLog?: boolean;
  includeNetworkLog?: boolean;
  requestValidation?: RequestValidationRule;
  enableRequestMonitoring?: boolean;
}

export class ResultCapture {
  private logger: Logger;
  private consoleMessages: string[] = [];
  private networkRequests: string[] = [];
  private requestValidationResults: RequestValidationResult[] = [];
  private pendingRequests: Map<string, NetworkRequest> = new Map();
  private validationRules: Map<string, RequestValidationRule> = new Map();

  constructor(private page: Page) {
    this.logger = new Logger('ResultCapture');
    this.setupPageListeners();
  }

  /**
   * 设置页面监听器
   */
  private setupPageListeners(): void {
    // 监听控制台消息
    this.page.on('console', (msg) => {
      const message = `[${msg.type()}] ${msg.text()}`;
      this.consoleMessages.push(message);

      // 限制消息数量，避免内存溢出
      if (this.consoleMessages.length > 1000) {
        this.consoleMessages = this.consoleMessages.slice(-500);
      }
    });

    // 监听网络请求
    this.page.on('request', (request) => {
      const requestInfo = `${request.method()} ${request.url()}`;
      this.networkRequests.push(requestInfo);

      // 记录详细的请求信息用于验证
      const requestId = uuidv4();
      const networkRequest: NetworkRequest = {
        id: requestId,
        url: request.url(),
        method: request.method(),
        headers: request.headers(),
        body: request.postData(),
        timestamp: new Date(),
        status: 'pending'
      };

      this.pendingRequests.set(requestId, networkRequest);

      // 限制请求记录数量
      if (this.networkRequests.length > 1000) {
        this.networkRequests = this.networkRequests.slice(-500);
      }
    });

    // 监听网络响应
    this.page.on('response', async (response) => {
      try {
        const url = response.url();
        const method = response.request().method();
        const statusCode = response.status();
        const responseTime = Date.now() - response.request().timing().startTime;

        // 查找对应的请求
        let requestId: string | undefined;
        for (const [id, req] of this.pendingRequests.entries()) {
          if (req.url === url && req.method === method) {
            requestId = id;
            break;
          }
        }

        if (requestId) {
          const request = this.pendingRequests.get(requestId);
          if (request) {
            request.status = statusCode >= 200 && statusCode < 400 ? 'completed' : 'failed';
            this.pendingRequests.delete(requestId);
          }
        }

        // 检查是否有验证规则
        const validationRule = this.validationRules.get(url);
        if (validationRule) {
          const validationResult = await this.validateResponse(response, validationRule);
          this.requestValidationResults.push(validationResult);

          this.logger.info('Request validation completed', {
            url,
            isValid: validationResult.isValid,
            errors: validationResult.errors
          });
        }

      } catch (error) {
        this.logger.error('Error processing response', error);
      }
    });

    // 监听页面错误
    this.page.on('pageerror', (error) => {
      this.logger.error('Page error detected', error);
      this.consoleMessages.push(`[ERROR] ${error.message}`);
    });
  }

  /**
   * 捕获测试步骤结果
   */
  async captureStepResult(
    stepId: string,
    executionId: string,
    options: CaptureOptions = {}
  ): Promise<Partial<TestStepResult>> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const result: Partial<TestStepResult> = {
      logs: [],
    };

    try {
      // 确保截图目录存在
      await ensureDirectoryExists(config.storage.screenshotDir);

      // 捕获截图
      if (options.includeScreenshot !== false) {
        const screenshotPath = await this.captureScreenshot(executionId, stepId, timestamp);
        if (screenshotPath) {
          result.screenshot = screenshotPath;
        }
      }

      // 捕获页面源码
      if (options.includePageSource) {
        const pageSource = await this.capturePageSource(executionId, stepId, timestamp);
        if (pageSource) {
          result.logs?.push(`Page source saved: ${pageSource}`);
        }
      }

      // 捕获控制台日志
      if (options.includeConsoleLog !== false) {
        const recentConsoleMessages = this.getRecentConsoleMessages(10);
        result.logs?.push(...recentConsoleMessages);
      }

      // 捕获网络日志
      if (options.includeNetworkLog) {
        const recentNetworkRequests = this.getRecentNetworkRequests(5);
        result.logs?.push(...recentNetworkRequests.map(req => `Network: ${req}`));
      }

      // 捕获页面信息
      const pageInfo = await this.capturePageInfo();
      result.logs?.push(`Page URL: ${pageInfo.url}`);
      result.logs?.push(`Page Title: ${pageInfo.title}`);

      this.logger.debug('Step result captured', { stepId, executionId });

    } catch (error) {
      this.logger.error('Failed to capture step result', error);
      result.logs?.push(`Capture error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return result;
  }

  /**
   * 捕获错误状态
   */
  async captureErrorState(
    stepId: string,
    executionId: string,
    error: Error
  ): Promise<Partial<TestStepResult>> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const result: Partial<TestStepResult> = {
      error: error.message,
      logs: [],
    };

    try {
      // 错误截图
      const screenshotPath = await this.captureScreenshot(executionId, stepId, timestamp, 'error');
      if (screenshotPath) {
        result.screenshot = screenshotPath;
      }

      // 捕获详细错误信息
      result.logs?.push(`Error: ${error.message}`);
      if (error.stack) {
        result.logs?.push(`Stack: ${error.stack}`);
      }

      // 捕获页面状态
      const pageInfo = await this.capturePageInfo();
      result.logs?.push(`Error occurred on: ${pageInfo.url}`);
      result.logs?.push(`Page title: ${pageInfo.title}`);

      // 捕获最近的控制台消息
      const recentConsoleMessages = this.getRecentConsoleMessages(20);
      result.logs?.push('Recent console messages:');
      result.logs?.push(...recentConsoleMessages);

      // 保存页面源码用于调试
      const pageSource = await this.capturePageSource(executionId, stepId, timestamp, 'error');
      if (pageSource) {
        result.logs?.push(`Error page source saved: ${pageSource}`);
      }

      this.logger.info('Error state captured', { stepId, executionId, error: error.message });

    } catch (captureError) {
      this.logger.error('Failed to capture error state', captureError);
      result.logs?.push(`Capture error: ${captureError instanceof Error ? captureError.message : String(captureError)}`);
    }

    return result;
  }

  /**
   * 捕获截图
   */
  private async captureScreenshot(
    executionId: string,
    stepId: string,
    timestamp: string,
    type: 'normal' | 'error' = 'normal'
  ): Promise<string | null> {
    try {
      const filename = `${executionId}_${stepId}_${type}_${timestamp}.png`;
      const filepath = path.join(config.storage.screenshotDir, filename);

      await this.page.screenshot({
        path: filepath,
        fullPage: true,
        type: 'png',
      });

      this.logger.debug('Screenshot captured', { filepath });
      return filepath;

    } catch (error) {
      this.logger.error('Failed to capture screenshot', error);
      return null;
    }
  }

  /**
   * 捕获页面源码
   */
  private async capturePageSource(
    executionId: string,
    stepId: string,
    timestamp: string,
    type: 'normal' | 'error' = 'normal'
  ): Promise<string | null> {
    try {
      const filename = `${executionId}_${stepId}_${type}_${timestamp}.html`;
      const filepath = path.join(config.storage.screenshotDir, filename);

      const content = await this.page.content();
      await fs.writeFile(filepath, content, 'utf-8');

      this.logger.debug('Page source captured', { filepath });
      return filepath;

    } catch (error) {
      this.logger.error('Failed to capture page source', error);
      return null;
    }
  }

  /**
   * 捕获页面信息
   */
  private async capturePageInfo(): Promise<{ url: string; title: string }> {
    try {
      const url = this.page.url();
      const title = await this.page.title();
      return { url, title };
    } catch (error) {
      this.logger.error('Failed to capture page info', error);
      return { url: 'unknown', title: 'unknown' };
    }
  }

  /**
   * 获取最近的控制台消息
   */
  private getRecentConsoleMessages(count: number): string[] {
    return this.consoleMessages.slice(-count);
  }

  /**
   * 获取最近的网络请求
   */
  private getRecentNetworkRequests(count: number): string[] {
    return this.networkRequests.slice(-count);
  }

  /**
   * 生成测试报告
   */
  async generateTestReport(execution: TestExecution): Promise<string> {
    try {
      const reportId = uuidv4();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `report_${execution.id}_${timestamp}.html`;
      const filepath = path.join(config.storage.screenshotDir, filename);

      const reportHtml = this.generateReportHtml(execution);
      await fs.writeFile(filepath, reportHtml, 'utf-8');

      this.logger.info('Test report generated', { filepath, executionId: execution.id });
      return filepath;

    } catch (error) {
      this.logger.error('Failed to generate test report', error);
      throw error;
    }
  }

  /**
   * 生成HTML报告内容
   */
  private generateReportHtml(execution: TestExecution): string {
    const duration = execution.endTime 
      ? execution.endTime.getTime() - execution.startTime.getTime()
      : 0;

    const passedSteps = execution.results.filter(r => r.status === 'passed').length;
    const failedSteps = execution.results.filter(r => r.status === 'failed').length;

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告 - ${execution.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e3f2fd; padding: 15px; border-radius: 5px; text-align: center; }
        .step { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .step.passed { border-left: 5px solid #4caf50; }
        .step.failed { border-left: 5px solid #f44336; }
        .screenshot { max-width: 300px; margin: 10px 0; }
        .logs { background: #f9f9f9; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="header">
        <h1>自动化测试报告</h1>
        <p><strong>执行ID:</strong> ${execution.id}</p>
        <p><strong>开始时间:</strong> ${execution.startTime.toLocaleString()}</p>
        <p><strong>结束时间:</strong> ${execution.endTime?.toLocaleString() || '进行中'}</p>
        <p><strong>执行时长:</strong> ${Math.round(duration / 1000)}秒</p>
        <p><strong>状态:</strong> ${execution.status}</p>
    </div>

    <div class="summary">
        <div class="metric">
            <h3>${execution.results.length}</h3>
            <p>总步骤数</p>
        </div>
        <div class="metric">
            <h3>${passedSteps}</h3>
            <p>成功步骤</p>
        </div>
        <div class="metric">
            <h3>${failedSteps}</h3>
            <p>失败步骤</p>
        </div>
        <div class="metric">
            <h3>${Math.round((passedSteps / execution.results.length) * 100)}%</h3>
            <p>成功率</p>
        </div>
    </div>

    <h2>步骤详情</h2>
    ${execution.results.map((step, index) => `
        <div class="step ${step.status}">
            <h3>步骤 ${index + 1} - ${step.status.toUpperCase()}</h3>
            ${step.screenshot ? `<img src="${step.screenshot}" alt="截图" class="screenshot">` : ''}
            ${step.error ? `<p><strong>错误:</strong> ${step.error}</p>` : ''}
            ${step.logs.length > 0 ? `
                <h4>日志:</h4>
                <div class="logs">${step.logs.join('<br>')}</div>
            ` : ''}
        </div>
    `).join('')}
</body>
</html>
    `;
  }

  /**
   * 验证HTTP响应
   */
  private async validateResponse(response: any, rule: RequestValidationRule): Promise<RequestValidationResult> {
    const url = response.url();
    const method = response.request().method();
    const statusCode = response.status();
    const responseTime = Date.now() - response.request().timing().startTime;
    const contentType = response.headers()['content-type'] || '';

    const result: RequestValidationResult = {
      url,
      method,
      statusCode,
      responseTime,
      contentType,
      isValid: true,
      errors: [],
      warnings: [],
      timestamp: new Date()
    };

    try {
      // 验证状态码
      if (rule.expectedStatusCode) {
        const expectedCodes = Array.isArray(rule.expectedStatusCode)
          ? rule.expectedStatusCode
          : [rule.expectedStatusCode];

        if (!expectedCodes.includes(statusCode)) {
          result.isValid = false;
          result.errors.push(`Expected status code ${expectedCodes.join(' or ')}, got ${statusCode}`);
        }
      }

      // 验证响应时间
      if (rule.maxResponseTime && responseTime > rule.maxResponseTime) {
        result.isValid = false;
        result.errors.push(`Response time ${responseTime}ms exceeds maximum ${rule.maxResponseTime}ms`);
      }

      // 验证内容类型
      if (rule.expectedContentType && !contentType.includes(rule.expectedContentType)) {
        result.isValid = false;
        result.errors.push(`Expected content type ${rule.expectedContentType}, got ${contentType}`);
      }

      // 获取响应数据用于进一步验证
      let responseData: any;
      try {
        if (contentType.includes('application/json')) {
          responseData = await response.json();
          result.responseData = responseData;
        } else {
          responseData = await response.text();
          result.responseData = responseData;
        }
      } catch (error) {
        result.warnings.push('Failed to parse response data');
      }

      // 验证结果数量
      if (rule.expectedResultCount && responseData) {
        const resultCount = this.extractResultCount(responseData, rule);
        result.resultCount = resultCount;

        if (rule.expectedResultCount.exact !== undefined) {
          if (resultCount !== rule.expectedResultCount.exact) {
            result.isValid = false;
            result.errors.push(`Expected exactly ${rule.expectedResultCount.exact} results, got ${resultCount}`);
          }
        }

        if (rule.expectedResultCount.min !== undefined && resultCount < rule.expectedResultCount.min) {
          result.isValid = false;
          result.errors.push(`Expected at least ${rule.expectedResultCount.min} results, got ${resultCount}`);
        }

        if (rule.expectedResultCount.max !== undefined && resultCount > rule.expectedResultCount.max) {
          result.isValid = false;
          result.errors.push(`Expected at most ${rule.expectedResultCount.max} results, got ${resultCount}`);
        }
      }

      // 验证响应内容
      if (rule.responseValidation && responseData) {
        const contentValidation = this.validateResponseContent(responseData, rule.responseValidation);
        if (!contentValidation.isValid) {
          result.isValid = false;
          result.errors.push(...contentValidation.errors);
        }
      }

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return result;
  }

  /**
   * 提取结果数量
   */
  private extractResultCount(data: any, rule: RequestValidationRule): number {
    if (Array.isArray(data)) {
      return data.length;
    }

    if (typeof data === 'object' && data !== null) {
      // 尝试常见的结果数组字段
      const commonFields = ['data', 'results', 'items', 'list', 'records'];
      for (const field of commonFields) {
        if (Array.isArray(data[field])) {
          return data[field].length;
        }
      }

      // 尝试count字段
      if (typeof data.count === 'number') {
        return data.count;
      }
      if (typeof data.total === 'number') {
        return data.total;
      }
    }

    return 0;
  }

  /**
   * 验证响应内容
   */
  private validateResponseContent(data: any, validation: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // JSON路径验证
      if (validation.jsonPath && validation.expectedValue !== undefined) {
        const actualValue = this.getValueByPath(data, validation.jsonPath);
        if (actualValue !== validation.expectedValue) {
          errors.push(`JSON path ${validation.jsonPath}: expected ${validation.expectedValue}, got ${actualValue}`);
        }
      }

      // 包含文本验证
      if (validation.contains) {
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);
        if (!dataString.includes(validation.contains)) {
          errors.push(`Response does not contain expected text: ${validation.contains}`);
        }
      }

      // 正则表达式验证
      if (validation.regex) {
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);
        const regex = new RegExp(validation.regex);
        if (!regex.test(dataString)) {
          errors.push(`Response does not match regex: ${validation.regex}`);
        }
      }

    } catch (error) {
      errors.push(`Content validation error: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 根据路径获取值
   */
  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 添加URL验证规则
   */
  addValidationRule(url: string, rule: RequestValidationRule): void {
    this.validationRules.set(url, rule);
    this.logger.debug('Added validation rule for URL', { url, rule });
  }

  /**
   * 获取验证结果
   */
  getValidationResults(): RequestValidationResult[] {
    return [...this.requestValidationResults];
  }

  /**
   * 获取最新的验证结果
   */
  getLatestValidationResult(url?: string): RequestValidationResult | undefined {
    const results = url
      ? this.requestValidationResults.filter(r => r.url === url)
      : this.requestValidationResults;

    return results.length > 0 ? results[results.length - 1] : undefined;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.consoleMessages = [];
    this.networkRequests = [];
    this.requestValidationResults = [];
    this.pendingRequests.clear();
    this.validationRules.clear();
    this.logger.debug('ResultCapture cleaned up');
  }
}
