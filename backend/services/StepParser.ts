import { ParsedStep, ActionType } from '../../shared/types';
import { Logger } from '../utils/logger';

export class StepParser {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('StepParser');
  }

  /**
   * 解析自然语言步骤
   */
  parseStep(stepText: string): ParsedStep {
    const normalizedText = this.normalizeText(stepText);
    
    // 检测动作类型
    const actionType = this.detectActionType(normalizedText);
    
    // 提取目标和值
    const { target, value } = this.extractTargetAndValue(normalizedText, actionType);
    
    // 计算置信度
    const confidence = this.calculateConfidence(normalizedText, actionType);
    
    // 生成建议
    const suggestions = this.generateSuggestions(normalizedText, actionType);

    const parsedStep: ParsedStep = {
      originalText: stepText,
      action: actionType,
      target,
      value,
      confidence,
      suggestions,
    };

    this.logger.debug('Parsed step', { 
      original: stepText, 
      parsed: parsedStep 
    });

    return parsedStep;
  }

  /**
   * 批量解析步骤
   */
  parseSteps(steps: string[]): ParsedStep[] {
    return steps.map(step => this.parseStep(step));
  }

  /**
   * 验证步骤的有效性
   */
  validateStep(parsedStep: ParsedStep): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查置信度
    if (parsedStep.confidence < 0.5) {
      errors.push('步骤描述不够清晰，建议使用更具体的描述');
    }

    // 检查必需的参数
    switch (parsedStep.action) {
      case ActionType.NAVIGATE:
        if (!parsedStep.target) {
          errors.push('导航操作需要指定目标URL');
        }
        break;
      case ActionType.CLICK:
        if (!parsedStep.target) {
          errors.push('点击操作需要指定目标元素');
        }
        break;
      case ActionType.TYPE:
        if (!parsedStep.target || !parsedStep.value) {
          errors.push('输入操作需要指定目标元素和输入内容');
        }
        break;
      case ActionType.SELECT:
        if (!parsedStep.target || !parsedStep.value) {
          errors.push('选择操作需要指定目标元素和选择值');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 标准化文本
   */
  private normalizeText(text: string): string {
    return text
      .trim()
      .toLowerCase()
      .replace(/，/g, ',')
      .replace(/。/g, '.')
      .replace(/！/g, '!')
      .replace(/？/g, '?')
      .replace(/；/g, ';')
      .replace(/：/g, ':')
      .replace(/"/g, '"')
      .replace(/"/g, '"')
      .replace(/'/g, "'")
      .replace(/'/g, "'")
      .replace(/（/g, '(')
      .replace(/）/g, ')')
      .replace(/【/g, '[')
      .replace(/】/g, ']');
  }

  /**
   * 检测动作类型
   */
  private detectActionType(text: string): ActionType {
    // 导航相关关键词
    const navigateKeywords = [
      '打开', '访问', '进入', '跳转', '导航', 'open', 'visit', 'go to', 'navigate'
    ];
    
    // 点击相关关键词
    const clickKeywords = [
      '点击', '单击', '按', '选择', 'click', 'tap', 'press'
    ];
    
    // 输入相关关键词
    const typeKeywords = [
      '输入', '填写', '键入', '写入', 'type', 'input', 'enter', 'fill'
    ];
    
    // 等待相关关键词
    const waitKeywords = [
      '等待', '暂停', '延迟', 'wait', 'pause', 'delay'
    ];
    
    // 验证相关关键词
    const verifyKeywords = [
      '验证', '检查', '确认', '断言', 'verify', 'check', 'assert', 'ensure'
    ];
    
    // 提取相关关键词
    const extractKeywords = [
      '提取', '获取', '读取', 'extract', 'get', 'read', 'capture'
    ];
    
    // 滚动相关关键词
    const scrollKeywords = [
      '滚动', '滑动', 'scroll', 'swipe'
    ];
    
    // 悬停相关关键词
    const hoverKeywords = [
      '悬停', '鼠标悬停', 'hover', 'mouseover'
    ];
    
    // 截图相关关键词
    const screenshotKeywords = [
      '截图', '截屏', '保存图片', 'screenshot', 'capture screen'
    ];

    // 按优先级检测
    if (navigateKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.NAVIGATE;
    }
    if (typeKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.TYPE;
    }
    if (clickKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.CLICK;
    }
    if (waitKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.WAIT;
    }
    if (verifyKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.VERIFY;
    }
    if (extractKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.EXTRACT;
    }
    if (scrollKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.SCROLL;
    }
    if (hoverKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.HOVER;
    }
    if (screenshotKeywords.some(keyword => text.includes(keyword))) {
      return ActionType.SCREENSHOT;
    }

    return ActionType.CUSTOM;
  }

  /**
   * 提取目标和值
   */
  private extractTargetAndValue(text: string, actionType: ActionType): { target?: string; value?: string } {
    let target: string | undefined;
    let value: string | undefined;

    switch (actionType) {
      case ActionType.NAVIGATE:
        // 提取URL
        const urlMatch = text.match(/(https?:\/\/[^\s]+|www\.[^\s]+|[^\s]+\.(com|cn|org|net|edu)[^\s]*)/);
        if (urlMatch) {
          target = urlMatch[0];
        } else {
          // 提取网站名称
          const siteMatch = text.match(/(?:打开|访问|进入)[\s]*([^\s]+)/);
          if (siteMatch) {
            target = siteMatch[1];
          }
        }
        break;

      case ActionType.CLICK:
        // 提取点击目标
        const clickTargets = [
          /(?:点击|单击|按)[\s]*["']?([^"']+)["']?/,
          /["']([^"']+)["'][\s]*(?:按钮|链接|元素)/,
        ];
        for (const pattern of clickTargets) {
          const match = text.match(pattern);
          if (match) {
            target = match[1].trim();
            break;
          }
        }
        break;

      case ActionType.TYPE:
        // 提取输入目标和内容
        const typePatterns = [
          /(?:在|向)[\s]*["']?([^"']+)["']?[\s]*(?:中|里|输入|填写)[\s]*["']?([^"']+)["']?/,
          /(?:输入|填写)[\s]*["']?([^"']+)["']?[\s]*(?:到|在)[\s]*["']?([^"']+)["']?/,
        ];
        for (const pattern of typePatterns) {
          const match = text.match(pattern);
          if (match) {
            target = match[1].trim();
            value = match[2].trim();
            break;
          }
        }
        break;

      case ActionType.WAIT:
        // 提取等待时间或条件
        const waitMatch = text.match(/(?:等待|暂停)[\s]*(\d+)[\s]*(?:秒|毫秒|分钟)?/);
        if (waitMatch) {
          value = waitMatch[1];
        }
        break;

      case ActionType.VERIFY:
        // 提取验证目标和期望值
        const verifyPatterns = [
          /(?:验证|检查)[\s]*["']?([^"']+)["']?[\s]*(?:包含|等于|显示)[\s]*["']?([^"']+)["']?/,
          /(?:确认|断言)[\s]*["']?([^"']+)["']?/,
        ];
        for (const pattern of verifyPatterns) {
          const match = text.match(pattern);
          if (match) {
            target = match[1].trim();
            if (match[2]) {
              value = match[2].trim();
            }
            break;
          }
        }
        break;
    }

    return { target, value };
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(text: string, actionType: ActionType): number {
    let confidence = 0.5; // 基础置信度

    // 根据动作类型调整置信度
    if (actionType !== ActionType.CUSTOM) {
      confidence += 0.3;
    }

    // 如果包含具体的目标描述，增加置信度
    if (text.includes('"') || text.includes("'") || text.includes('【') || text.includes('】')) {
      confidence += 0.2;
    }

    // 如果文本长度适中，增加置信度
    if (text.length > 5 && text.length < 100) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 生成建议
   */
  private generateSuggestions(text: string, actionType: ActionType): string[] {
    const suggestions: string[] = [];

    if (actionType === ActionType.CUSTOM) {
      suggestions.push('建议使用更明确的动作词，如：点击、输入、验证等');
    }

    if (!text.includes('"') && !text.includes("'")) {
      suggestions.push('建议使用引号标记具体的元素名称，如："登录按钮"');
    }

    if (text.length < 5) {
      suggestions.push('建议提供更详细的步骤描述');
    }

    return suggestions;
  }
}
