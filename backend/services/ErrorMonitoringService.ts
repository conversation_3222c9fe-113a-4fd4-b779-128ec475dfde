import { Stagehand } from '@browserbasehq/stagehand';
import { Page, Response } from 'playwright';
import { Logger } from '../utils/logger';
import { ConsoleError, NetworkError, ErrorType } from '@shared/types/websiteTest';
import path from 'path';
import fs from 'fs/promises';

export interface ErrorMonitoringConfig {
  captureScreenshots: boolean;
  captureConsoleLogs: boolean;
  captureNetworkErrors: boolean;
  monitorJavaScriptErrors: boolean;
  screenshotDir: string;
  timeout: number;
  userAgent?: string;
}

export interface PageErrorReport {
  url: string;
  httpStatus?: number;
  responseTime: number;
  serverError: boolean;
  frontendError: boolean;
  consoleErrors: ConsoleError[];
  networkErrors: NetworkError[];
  screenshotPath?: string;
  errorMessage?: string;
  testedAt: Date;
}

export class ErrorMonitoringService {
  private logger: Logger;
  private stagehand: Stagehand | null = null;
  private config: ErrorMonitoringConfig;
  private consoleErrors: ConsoleError[] = [];
  private networkErrors: NetworkError[] = [];

  constructor(config: Partial<ErrorMonitoringConfig> = {}) {
    this.logger = new Logger('ErrorMonitoringService');
    this.config = {
      captureScreenshots: config.captureScreenshots ?? true,
      captureConsoleLogs: config.captureConsoleLogs ?? true,
      captureNetworkErrors: config.captureNetworkErrors ?? true,
      monitorJavaScriptErrors: config.monitorJavaScriptErrors ?? true,
      screenshotDir: config.screenshotDir || './screenshots',
      timeout: config.timeout || 30000,
      userAgent: config.userAgent
    };
  }

  /**
   * 初始化错误监控服务
   */
  async initialize(): Promise<void> {
    try {
      this.stagehand = new Stagehand({
        env: 'LOCAL'
      });
      
      await this.stagehand.init();
      
      // 确保截图目录存在
      await this.ensureScreenshotDir();
      
      this.logger.info('Error monitoring service initialized');
    } catch (error) {
      this.logger.error('Failed to initialize error monitoring service', error);
      throw error;
    }
  }

  /**
   * 监控单个页面的错误
   */
  async monitorPage(url: string): Promise<PageErrorReport> {
    if (!this.stagehand) {
      throw new Error('Service not initialized');
    }

    this.logger.info('Starting page error monitoring', { url });
    
    // 重置错误收集器
    this.consoleErrors = [];
    this.networkErrors = [];

    const startTime = Date.now();
    let response: Response | null = null;
    let screenshotPath: string | undefined;
    let errorMessage: string | undefined;

    try {
      // 设置错误监听器
      await this.setupErrorListeners();

      // 导航到页面
      response = await this.stagehand.page.goto(url, {
        waitUntil: 'networkidle',
        timeout: this.config.timeout
      });

      // 等待页面完全加载
      await this.stagehand.page.waitForTimeout(2000);

      // 检查JavaScript错误
      if (this.config.monitorJavaScriptErrors) {
        await this.checkJavaScriptErrors();
      }

      // 捕获截图（如果有错误或配置要求）
      if (this.config.captureScreenshots && (this.hasErrors() || this.config.captureScreenshots)) {
        screenshotPath = await this.captureScreenshot(url);
      }

    } catch (error) {
      errorMessage = error.message;
      this.logger.warn('Error occurred while monitoring page', { url, error: error.message });
      
      // 即使出错也尝试截图
      if (this.config.captureScreenshots) {
        try {
          screenshotPath = await this.captureScreenshot(url, 'error');
        } catch (screenshotError) {
          this.logger.warn('Failed to capture error screenshot', { url, error: screenshotError.message });
        }
      }
    }

    const responseTime = Date.now() - startTime;
    const httpStatus = response?.status();
    
    const report: PageErrorReport = {
      url,
      httpStatus,
      responseTime,
      serverError: this.isServerError(httpStatus),
      frontendError: this.hasFrontendErrors(),
      consoleErrors: [...this.consoleErrors],
      networkErrors: [...this.networkErrors],
      screenshotPath,
      errorMessage,
      testedAt: new Date()
    };

    this.logger.info('Page error monitoring completed', {
      url,
      httpStatus,
      responseTime,
      serverError: report.serverError,
      frontendError: report.frontendError,
      consoleErrorsCount: this.consoleErrors.length,
      networkErrorsCount: this.networkErrors.length
    });

    return report;
  }

  /**
   * 设置错误监听器
   */
  private async setupErrorListeners(): Promise<void> {
    if (!this.stagehand) return;

    const page = this.stagehand.page;

    // 监听控制台消息
    if (this.config.captureConsoleLogs) {
      page.on('console', (msg) => {
        const level = msg.type() as 'error' | 'warning' | 'info' | 'debug';
        if (['error', 'warning'].includes(level)) {
          this.consoleErrors.push({
            level,
            message: msg.text(),
            source: msg.location()?.url,
            line: msg.location()?.lineNumber,
            column: msg.location()?.columnNumber,
            timestamp: new Date()
          });
        }
      });
    }

    // 监听页面错误
    page.on('pageerror', (error) => {
      this.consoleErrors.push({
        level: 'error',
        message: error.message,
        source: error.stack?.split('\n')[1]?.trim(),
        timestamp: new Date()
      });
    });

    // 监听网络请求失败
    if (this.config.captureNetworkErrors) {
      page.on('requestfailed', (request) => {
        this.networkErrors.push({
          url: request.url(),
          method: request.method(),
          errorMessage: request.failure()?.errorText || 'Request failed',
          timestamp: new Date()
        });
      });

      // 监听响应错误
      page.on('response', (response) => {
        if (!response.ok()) {
          this.networkErrors.push({
            url: response.url(),
            method: response.request().method(),
            status: response.status(),
            errorMessage: `HTTP ${response.status()} ${response.statusText()}`,
            timestamp: new Date()
          });
        }
      });
    }
  }

  /**
   * 检查JavaScript错误
   */
  private async checkJavaScriptErrors(): Promise<void> {
    if (!this.stagehand) return;

    try {
      // 执行JavaScript来检查页面中的错误
      const jsErrors = await this.stagehand.page.evaluate(() => {
        const errors: any[] = [];
        
        // 检查是否有未捕获的异常
        if (window.onerror) {
          const originalOnError = window.onerror;
          window.onerror = function(message, source, lineno, colno, error) {
            errors.push({
              message: message?.toString() || 'Unknown error',
              source,
              line: lineno,
              column: colno,
              stack: error?.stack,
              timestamp: new Date().toISOString()
            });
            return originalOnError?.call(this, message, source, lineno, colno, error);
          };
        }

        // 检查Promise rejection
        if (window.addEventListener) {
          window.addEventListener('unhandledrejection', (event) => {
            errors.push({
              message: `Unhandled Promise Rejection: ${event.reason}`,
              source: 'Promise',
              timestamp: new Date().toISOString()
            });
          });
        }

        // 检查页面中是否有错误元素
        const errorElements = document.querySelectorAll('.error, .alert-danger, [class*="error"], [id*="error"]');
        errorElements.forEach((element, index) => {
          if (element.textContent?.trim()) {
            errors.push({
              message: `Page error element: ${element.textContent.trim()}`,
              source: `DOM element ${element.tagName}`,
              selector: element.className || element.id || `element-${index}`,
              timestamp: new Date().toISOString()
            });
          }
        });

        return errors;
      });

      // 将JavaScript错误添加到控制台错误列表
      jsErrors.forEach(error => {
        this.consoleErrors.push({
          level: 'error',
          message: error.message,
          source: error.source,
          line: error.line,
          column: error.column,
          timestamp: new Date(error.timestamp)
        });
      });

    } catch (error) {
      this.logger.warn('Failed to check JavaScript errors', { error: error.message });
    }
  }

  /**
   * 捕获截图
   */
  private async captureScreenshot(url: string, suffix: string = ''): Promise<string> {
    if (!this.stagehand) throw new Error('Service not initialized');

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const urlHash = Buffer.from(url).toString('base64').substring(0, 10);
      const filename = `${urlHash}_${timestamp}${suffix ? `_${suffix}` : ''}.png`;
      const screenshotPath = path.join(this.config.screenshotDir, filename);

      await this.stagehand.page.screenshot({
        path: screenshotPath,
        fullPage: true,
        type: 'png'
      });

      this.logger.debug('Screenshot captured', { url, screenshotPath });
      return screenshotPath;
    } catch (error) {
      this.logger.error('Failed to capture screenshot', { url, error: error.message });
      throw error;
    }
  }

  /**
   * 确保截图目录存在
   */
  private async ensureScreenshotDir(): Promise<void> {
    try {
      await fs.access(this.config.screenshotDir);
    } catch {
      await fs.mkdir(this.config.screenshotDir, { recursive: true });
      this.logger.info('Screenshot directory created', { dir: this.config.screenshotDir });
    }
  }

  /**
   * 检查是否为服务端错误
   */
  private isServerError(httpStatus?: number): boolean {
    return httpStatus ? httpStatus >= 500 : false;
  }

  /**
   * 检查是否有前端错误
   */
  private hasFrontendErrors(): boolean {
    return this.consoleErrors.some(error => error.level === 'error') ||
           this.networkErrors.some(error => error.status && error.status >= 400 && error.status < 500);
  }

  /**
   * 检查是否有任何错误
   */
  private hasErrors(): boolean {
    return this.consoleErrors.length > 0 || this.networkErrors.length > 0;
  }

  /**
   * 批量监控多个URL
   */
  async monitorMultiplePages(urls: string[]): Promise<PageErrorReport[]> {
    const reports: PageErrorReport[] = [];
    
    for (const url of urls) {
      try {
        const report = await this.monitorPage(url);
        reports.push(report);
        
        // 添加延迟避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        this.logger.error('Failed to monitor page', { url, error: error.message });
        
        // 创建错误报告
        reports.push({
          url,
          responseTime: 0,
          serverError: true,
          frontendError: false,
          consoleErrors: [],
          networkErrors: [],
          errorMessage: error.message,
          testedAt: new Date()
        });
      }
    }
    
    return reports;
  }

  /**
   * 获取错误类型
   */
  getErrorType(report: PageErrorReport): ErrorType[] {
    const errorTypes: ErrorType[] = [];
    
    if (report.serverError) {
      errorTypes.push('server_error');
    }
    
    if (report.frontendError) {
      errorTypes.push('frontend_error');
    }
    
    if (report.networkErrors.length > 0) {
      errorTypes.push('network_error');
    }
    
    if (report.responseTime > this.config.timeout) {
      errorTypes.push('timeout_error');
    }
    
    return errorTypes;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    if (this.stagehand) {
      await this.stagehand.close();
      this.stagehand = null;
    }
    this.consoleErrors = [];
    this.networkErrors = [];
    this.logger.info('Error monitoring service cleaned up');
  }
}
