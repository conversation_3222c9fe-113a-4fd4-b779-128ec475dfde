import { Page } from 'playwright';
import {
  ActionType,
  BrowserConfig,
  AutomationError,
  RequestValidationRule,
  RequestOrchestrationRule
} from '@shared/types';
import { Logger } from '../utils/logger';
import { config } from '../config';
import { ResultCapture } from './ResultCapture';
import { RequestOrchestrator } from './RequestOrchestrator';

export class BrowserController {
  private logger: Logger;
  private retryCount: number = 0;
  private maxRetries: number;
  private resultCapture: ResultCapture;
  private requestOrchestrator: RequestOrchestrator;

  constructor(private page: Page, browserConfig?: BrowserConfig) {
    this.logger = new Logger('BrowserController');
    this.maxRetries = config.ai.maxRetries;
    this.resultCapture = new ResultCapture(page);
    this.requestOrchestrator = new RequestOrchestrator(page, this.resultCapture);
  }

  /**
   * 执行导航操作
   */
  async navigate(url: string, validationRule?: RequestValidationRule): Promise<void> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Navigating to: ${url}`);

      // 处理相对URL和简化的网站名称
      const finalUrl = this.normalizeUrl(url);

      // 添加验证规则
      if (validationRule) {
        this.resultCapture.addValidationRule(finalUrl, validationRule);
      }

      await this.page.goto(finalUrl, {
        waitUntil: 'domcontentloaded',
        timeout: config.browser.timeout,
      });

      // 等待页面稳定
      await this.page.waitForLoadState('networkidle', { timeout: 5000 });

      // 检查验证结果
      if (validationRule) {
        const validationResult = this.resultCapture.getLatestValidationResult(finalUrl);
        if (validationResult) {
          // 触发编排规则检查
          await this.requestOrchestrator.checkAndExecuteTriggers(validationResult);

          if (!validationResult.isValid) {
            throw new Error(`URL validation failed: ${validationResult.errors.join(', ')}`);
          }
        }
      }

      this.logger.info(`Successfully navigated to: ${finalUrl}`);
    }, `Failed to navigate to ${url}`);
  }

  /**
   * 执行点击操作
   */
  async click(selector: string): Promise<void> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Clicking element: ${selector}`);
      
      // 智能选择器查找
      const element = await this.findElement(selector);
      
      if (!element) {
        throw new Error(`Element not found: ${selector}`);
      }
      
      // 确保元素可见和可点击
      await element.scrollIntoViewIfNeeded();
      await element.waitForElementState('visible');
      await element.waitForElementState('stable');
      
      await element.click();
      
      this.logger.info(`Successfully clicked: ${selector}`);
    }, `Failed to click element: ${selector}`);
  }

  /**
   * 执行输入操作
   */
  async type(selector: string, text: string, options?: { clear?: boolean }): Promise<void> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Typing into element: ${selector}`);
      
      const element = await this.findElement(selector);
      
      if (!element) {
        throw new Error(`Element not found: ${selector}`);
      }
      
      // 确保元素可见和可编辑
      await element.scrollIntoViewIfNeeded();
      await element.waitForElementState('visible');
      
      // 清空现有内容（如果需要）
      if (options?.clear !== false) {
        await element.clear();
      }
      
      // 输入文本
      await element.type(text, { delay: 50 }); // 添加延迟模拟真实输入
      
      this.logger.info(`Successfully typed into: ${selector}`);
    }, `Failed to type into element: ${selector}`);
  }

  /**
   * 执行选择操作
   */
  async select(selector: string, value: string): Promise<void> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Selecting option in: ${selector}`);
      
      const element = await this.findElement(selector);
      
      if (!element) {
        throw new Error(`Element not found: ${selector}`);
      }
      
      await element.scrollIntoViewIfNeeded();
      await element.selectOption(value);
      
      this.logger.info(`Successfully selected option: ${value}`);
    }, `Failed to select option in: ${selector}`);
  }

  /**
   * 执行等待操作
   */
  async wait(condition: string | number): Promise<void> {
    return this.executeWithRetry(async () => {
      if (typeof condition === 'number') {
        this.logger.info(`Waiting for ${condition}ms`);
        await this.page.waitForTimeout(condition);
      } else {
        this.logger.info(`Waiting for condition: ${condition}`);
        // 等待元素出现
        await this.page.waitForSelector(condition, { timeout: config.browser.timeout });
      }
      
      this.logger.info('Wait completed');
    }, `Failed to wait for: ${condition}`);
  }

  /**
   * 执行验证操作
   */
  async verify(selector: string, expectedValue?: string): Promise<boolean> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Verifying element: ${selector}`);
      
      const element = await this.findElement(selector);
      
      if (!element) {
        throw new Error(`Element not found for verification: ${selector}`);
      }
      
      if (expectedValue) {
        const actualValue = await element.textContent();
        const isMatch = actualValue?.includes(expectedValue) || false;
        
        if (!isMatch) {
          throw new Error(`Verification failed. Expected: ${expectedValue}, Actual: ${actualValue}`);
        }
        
        this.logger.info(`Verification passed: ${selector} contains "${expectedValue}"`);
      } else {
        // 仅验证元素存在
        this.logger.info(`Verification passed: ${selector} exists`);
      }
      
      return true;
    }, `Failed to verify element: ${selector}`);
  }

  /**
   * 执行滚动操作
   */
  async scroll(direction: 'up' | 'down' | 'top' | 'bottom', distance?: number): Promise<void> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Scrolling ${direction}`);
      
      switch (direction) {
        case 'up':
          await this.page.mouse.wheel(0, -(distance || 300));
          break;
        case 'down':
          await this.page.mouse.wheel(0, distance || 300);
          break;
        case 'top':
          await this.page.evaluate(() => window.scrollTo(0, 0));
          break;
        case 'bottom':
          await this.page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
          break;
      }
      
      // 等待滚动完成
      await this.page.waitForTimeout(500);
      
      this.logger.info(`Scroll ${direction} completed`);
    }, `Failed to scroll ${direction}`);
  }

  /**
   * 执行悬停操作
   */
  async hover(selector: string): Promise<void> {
    return this.executeWithRetry(async () => {
      this.logger.info(`Hovering over element: ${selector}`);
      
      const element = await this.findElement(selector);
      
      if (!element) {
        throw new Error(`Element not found: ${selector}`);
      }
      
      await element.scrollIntoViewIfNeeded();
      await element.hover();
      
      this.logger.info(`Successfully hovered over: ${selector}`);
    }, `Failed to hover over element: ${selector}`);
  }

  /**
   * 智能元素查找
   */
  private async findElement(selector: string) {
    // 尝试多种选择器策略
    const strategies = [
      // 直接选择器
      () => this.page.locator(selector).first(),
      // 文本内容匹配
      () => this.page.getByText(selector).first(),
      // 占位符匹配
      () => this.page.getByPlaceholder(selector).first(),
      // 标签匹配
      () => this.page.getByLabel(selector).first(),
      // 角色匹配
      () => this.page.getByRole('button', { name: selector }).first(),
      () => this.page.getByRole('link', { name: selector }).first(),
      () => this.page.getByRole('textbox', { name: selector }).first(),
      // 部分文本匹配
      () => this.page.locator(`text=${selector}`).first(),
      () => this.page.locator(`[placeholder*="${selector}"]`).first(),
      () => this.page.locator(`[title*="${selector}"]`).first(),
      () => this.page.locator(`[alt*="${selector}"]`).first(),
    ];

    for (const strategy of strategies) {
      try {
        const element = strategy();
        const count = await element.count();
        if (count > 0) {
          return element;
        }
      } catch (error) {
        // 继续尝试下一个策略
        continue;
      }
    }

    return null;
  }

  /**
   * 标准化URL
   */
  private normalizeUrl(url: string): string {
    // 如果已经是完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    // 如果是www开头，添加https
    if (url.startsWith('www.')) {
      return `https://${url}`;
    }
    
    // 常见网站映射
    const siteMap: { [key: string]: string } = {
      '百度': 'https://www.baidu.com',
      '谷歌': 'https://www.google.com',
      '淘宝': 'https://www.taobao.com',
      '京东': 'https://www.jd.com',
      '微博': 'https://weibo.com',
      '知乎': 'https://www.zhihu.com',
      'github': 'https://github.com',
    };
    
    const lowerUrl = url.toLowerCase();
    for (const [key, value] of Object.entries(siteMap)) {
      if (lowerUrl.includes(key.toLowerCase())) {
        return value;
      }
    }
    
    // 默认添加https和www
    return `https://www.${url}`;
  }

  /**
   * 添加URL验证规则
   */
  addValidationRule(url: string, rule: RequestValidationRule): void {
    this.resultCapture.addValidationRule(url, rule);
    this.logger.debug('Added validation rule', { url });
  }

  /**
   * 添加请求编排规则
   */
  addOrchestrationRule(rule: RequestOrchestrationRule): void {
    this.requestOrchestrator.addRule(rule);
    this.logger.debug('Added orchestration rule', { ruleId: rule.id, ruleName: rule.name });
  }

  /**
   * 初始化编排上下文
   */
  initializeOrchestrationContext(executionId: string): void {
    this.requestOrchestrator.initializeContext(executionId);
  }

  /**
   * 获取验证结果
   */
  getValidationResults() {
    return this.resultCapture.getValidationResults();
  }

  /**
   * 获取编排上下文
   */
  getOrchestrationContext() {
    return this.requestOrchestrator.getContext();
  }

  /**
   * 设置编排变量
   */
  setOrchestrationVariable(name: string, value: any): void {
    this.requestOrchestrator.setVariable(name, value);
  }

  /**
   * 获取编排变量
   */
  getOrchestrationVariable(name: string): any {
    return this.requestOrchestrator.getVariable(name);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.resultCapture.cleanup();
    this.requestOrchestrator.cleanup();
    this.logger.debug('BrowserController cleaned up');
  }

  /**
   * 重试机制执行
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    errorMessage: string
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < this.maxRetries) {
          this.logger.warn(`Operation failed, retrying (${attempt + 1}/${this.maxRetries})`, lastError);
          await this.page.waitForTimeout(1000 * (attempt + 1)); // 递增延迟
        }
      }
    }

    this.logger.error(errorMessage, lastError);
    throw new Error(`${errorMessage}: ${lastError?.message}`);
  }
}
