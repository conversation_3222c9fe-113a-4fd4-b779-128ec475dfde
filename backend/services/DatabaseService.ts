import sqlite3 from 'sqlite3';
import { Database, open } from 'sqlite';
import path from 'path';
import { TestExecution, TestCase } from '@shared/types';
import { Logger } from '../utils/logger';
import { config } from '../config';
import { ensureDirectoryExists } from '../utils/fileSystem';

export class DatabaseService {
  private db: Database | null = null;
  private logger: Logger;

  constructor() {
    this.logger = new Logger('DatabaseService');
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<void> {
    try {
      // 确保数据库目录存在
      const dbDir = path.dirname(config.storage.databasePath);
      await ensureDirectoryExists(dbDir);

      // 打开数据库连接
      this.db = await open({
        filename: config.storage.databasePath,
        driver: sqlite3.Database,
      });

      // 创建表结构
      await this.createTables();
      
      this.logger.info('Database initialized successfully', {
        path: config.storage.databasePath,
      });
    } catch (error) {
      this.logger.error('Failed to initialize database', error);
      throw error;
    }
  }

  /**
   * 创建数据库表结构
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // 测试用例表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS test_cases (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        steps TEXT NOT NULL, -- JSON格式存储步骤
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        tags TEXT -- JSON格式存储标签
      )
    `);

    // 测试执行表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS test_executions (
        id TEXT PRIMARY KEY,
        test_case_id TEXT,
        status TEXT NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME,
        results TEXT, -- JSON格式存储结果
        screenshots TEXT, -- JSON格式存储截图路径
        logs TEXT, -- JSON格式存储日志
        error TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建索引
    await this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_executions_status ON test_executions(status);
      CREATE INDEX IF NOT EXISTS idx_executions_start_time ON test_executions(start_time);
      CREATE INDEX IF NOT EXISTS idx_executions_test_case_id ON test_executions(test_case_id);
    `);

    this.logger.info('Database tables created successfully');
  }

  /**
   * 保存测试执行记录
   */
  async saveExecution(execution: TestExecution): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run(`
        INSERT OR REPLACE INTO test_executions (
          id, test_case_id, status, start_time, end_time, 
          results, screenshots, logs, error
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        execution.id,
        execution.testCaseId || null,
        execution.status,
        execution.startTime.toISOString(),
        execution.endTime?.toISOString() || null,
        JSON.stringify(execution.results),
        JSON.stringify(execution.screenshots),
        JSON.stringify(execution.logs),
        execution.error || null,
      ]);

      this.logger.info('Execution saved successfully', { executionId: execution.id });
    } catch (error) {
      this.logger.error('Failed to save execution', error);
      throw error;
    }
  }

  /**
   * 获取测试执行记录
   */
  async getExecution(executionId: string): Promise<TestExecution | null> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const row = await this.db.get(`
        SELECT * FROM test_executions WHERE id = ?
      `, [executionId]);

      if (!row) return null;

      return this.mapRowToExecution(row);
    } catch (error) {
      this.logger.error('Failed to get execution', error);
      throw error;
    }
  }

  /**
   * 获取测试执行历史
   */
  async getExecutions(options: {
    page?: number;
    limit?: number;
    status?: string;
  } = {}): Promise<TestExecution[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const { page = 1, limit = 20, status } = options;
      const offset = (page - 1) * limit;

      let query = `
        SELECT * FROM test_executions
      `;
      const params: any[] = [];

      if (status) {
        query += ` WHERE status = ?`;
        params.push(status);
      }

      query += ` ORDER BY start_time DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const rows = await this.db.all(query, params);

      return rows.map(row => this.mapRowToExecution(row));
    } catch (error) {
      this.logger.error('Failed to get executions', error);
      throw error;
    }
  }

  /**
   * 保存测试用例
   */
  async saveTestCase(testCase: TestCase): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run(`
        INSERT OR REPLACE INTO test_cases (
          id, name, description, steps, tags, updated_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `, [
        testCase.id,
        testCase.name,
        testCase.description,
        JSON.stringify(testCase.steps),
        JSON.stringify(testCase.tags),
      ]);

      this.logger.info('Test case saved successfully', { testCaseId: testCase.id });
    } catch (error) {
      this.logger.error('Failed to save test case', error);
      throw error;
    }
  }

  /**
   * 获取测试用例
   */
  async getTestCase(testCaseId: string): Promise<TestCase | null> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const row = await this.db.get(`
        SELECT * FROM test_cases WHERE id = ?
      `, [testCaseId]);

      if (!row) return null;

      return this.mapRowToTestCase(row);
    } catch (error) {
      this.logger.error('Failed to get test case', error);
      throw error;
    }
  }

  /**
   * 获取所有测试用例
   */
  async getTestCases(): Promise<TestCase[]> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const rows = await this.db.all(`
        SELECT * FROM test_cases ORDER BY updated_at DESC
      `);

      return rows.map(row => this.mapRowToTestCase(row));
    } catch (error) {
      this.logger.error('Failed to get test cases', error);
      throw error;
    }
  }

  /**
   * 删除测试用例
   */
  async deleteTestCase(testCaseId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      await this.db.run(`
        DELETE FROM test_cases WHERE id = ?
      `, [testCaseId]);

      this.logger.info('Test case deleted successfully', { testCaseId });
    } catch (error) {
      this.logger.error('Failed to delete test case', error);
      throw error;
    }
  }

  /**
   * 获取执行统计信息
   */
  async getExecutionStats(): Promise<{
    total: number;
    completed: number;
    failed: number;
    running: number;
  }> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      const stats = await this.db.get(`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
          SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running
        FROM test_executions
      `);

      return {
        total: stats.total || 0,
        completed: stats.completed || 0,
        failed: stats.failed || 0,
        running: stats.running || 0,
      };
    } catch (error) {
      this.logger.error('Failed to get execution stats', error);
      throw error;
    }
  }

  /**
   * 将数据库行映射为TestExecution对象
   */
  private mapRowToExecution(row: any): TestExecution {
    return {
      id: row.id,
      testCaseId: row.test_case_id || '',
      status: row.status,
      startTime: new Date(row.start_time),
      endTime: row.end_time ? new Date(row.end_time) : undefined,
      results: JSON.parse(row.results || '[]'),
      screenshots: JSON.parse(row.screenshots || '[]'),
      logs: JSON.parse(row.logs || '[]'),
      error: row.error || undefined,
    };
  }

  /**
   * 将数据库行映射为TestCase对象
   */
  private mapRowToTestCase(row: any): TestCase {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      steps: JSON.parse(row.steps),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      tags: JSON.parse(row.tags || '[]'),
    };
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      this.logger.info('Database connection closed');
    }
  }
}
