import { GeminiService } from './services/GeminiService';

// 简化版自动化引擎，集成Gemini AI
export class SimpleAutomationEngine {
  private executionId: string;
  private steps: string[];
  private currentStepIndex: number = 0;
  private status: 'pending' | 'running' | 'completed' | 'failed' = 'pending';
  private results: any[] = [];
  private geminiService: GeminiService;

  constructor(executionId: string, steps: string[]) {
    this.executionId = executionId;
    this.steps = steps;
    this.geminiService = new GeminiService();
  }

  async executeSteps(): Promise<any> {
    this.status = 'running';

    // 使用Gemini优化测试步骤
    console.log('正在使用AI优化测试步骤...');
    const analysis = await this.geminiService.analyzeTestSteps(this.steps);
    console.log('AI分析完成:', analysis.suggestions);

    // 使用优化后的步骤
    const stepsToExecute = analysis.optimizedSteps.length > 0 ? analysis.optimizedSteps : this.steps;

    for (let i = 0; i < stepsToExecute.length; i++) {
      this.currentStepIndex = i;
      const step = stepsToExecute[i];
      
      console.log(`执行步骤 ${i + 1}: ${step}`);
      
      // 模拟执行时间
      await this.delay(1000 + Math.random() * 2000);
      
      // 模拟步骤结果
      const stepResult = {
        stepId: `step-${i}`,
        status: Math.random() > 0.1 ? 'passed' : 'failed', // 90%成功率
        startTime: new Date(),
        endTime: new Date(),
        logs: [`执行步骤: ${step}`, `步骤完成`],
        screenshot: `/screenshots/step-${i}.png`
      };
      
      this.results.push(stepResult);
      
      if (stepResult.status === 'failed') {
        this.status = 'failed';
        break;
      }
    }
    
    if (this.status === 'running') {
      this.status = 'completed';
    }
    
    return {
      id: this.executionId,
      status: this.status,
      startTime: new Date(),
      endTime: new Date(),
      results: this.results,
      screenshots: this.results.map(r => r.screenshot),
      logs: ['测试执行开始', '测试执行完成']
    };
  }

  getCurrentExecution() {
    return {
      id: this.executionId,
      status: this.status,
      startTime: new Date(),
      endTime: this.status === 'completed' || this.status === 'failed' ? new Date() : undefined,
      results: this.results,
      screenshots: this.results.map(r => r.screenshot),
      logs: ['测试执行中...']
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 简化版步骤解析器，集成Gemini AI
export class SimpleStepParser {
  private geminiService: GeminiService;

  constructor() {
    this.geminiService = new GeminiService();
  }

  async parseSteps(steps: string[]) {
    const parsedSteps = [];

    for (const step of steps) {
      // 使用Gemini进行智能解析
      const aiAnalysis = await this.geminiService.parseNaturalLanguage(step);

      parsedSteps.push({
        originalText: step,
        action: aiAnalysis.actionType || this.detectAction(step),
        target: this.extractTarget(step),
        value: this.extractValue(step),
        confidence: aiAnalysis.confidence,
        suggestions: aiAnalysis.steps.length > 1 ? [`AI建议分解为${aiAnalysis.steps.length}个步骤`] : [],
        aiOptimizedSteps: aiAnalysis.steps
      });
    }

    return parsedSteps;
  }

  validateStep(parsedStep: any) {
    return {
      isValid: true,
      errors: []
    };
  }

  private detectAction(step: string): string {
    if (step.includes('打开') || step.includes('访问')) return 'navigate';
    if (step.includes('点击') || step.includes('单击')) return 'click';
    if (step.includes('输入') || step.includes('填写')) return 'type';
    if (step.includes('验证') || step.includes('检查')) return 'verify';
    if (step.includes('等待') || step.includes('暂停')) return 'wait';
    return 'custom';
  }

  private extractTarget(step: string): string {
    // 简单的目标提取
    const matches = step.match(/["']([^"']+)["']/);
    if (matches) return matches[1];
    
    if (step.includes('搜索框')) return '搜索框';
    if (step.includes('按钮')) return '按钮';
    if (step.includes('链接')) return '链接';
    
    return '未知元素';
  }

  private extractValue(step: string): string | undefined {
    const matches = step.match(/输入["']([^"']+)["']/);
    return matches ? matches[1] : undefined;
  }
}
