# AI模型配置
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Browserbase配置（可选，用于云端浏览器）
BROWSERBASE_API_KEY=your_browserbase_api_key_here
BROWSERBASE_PROJECT_ID=your_browserbase_project_id_here

# 服务器配置
PORT=3001
NODE_ENV=development

# 数据库配置
DATABASE_PATH=./data/automation.db

# MySQL数据库配置
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password_here
MYSQL_DATABASE=aitest
MYSQL_CONNECTION_LIMIT=10
MYSQL_ACQUIRE_TIMEOUT=60000
MYSQL_TIMEOUT=60000

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 浏览器配置
HEADLESS=false
BROWSER_TIMEOUT=30000
DEFAULT_VIEWPORT_WIDTH=1920
DEFAULT_VIEWPORT_HEIGHT=1080

# AI配置
DEFAULT_AI_PROVIDER=openai
DEFAULT_AI_MODEL=gpt-4
MAX_RETRY_ATTEMPTS=3
AI_TIMEOUT=60000

# 安全配置
CORS_ORIGIN=http://localhost:3000
JWT_SECRET=your_jwt_secret_here

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# 测试配置
TEST_TIMEOUT=120000
SCREENSHOT_DIR=./screenshots
