# 🌐 网站自动化测试平台

为 https://www.webopte.com/ 网站创建的专业浏览器自动化测试系统。

## ✨ 功能特性

- ✅ **服务端错误检测** - 监控HTTP状态码，检测5xx服务器错误
- ✅ **前端错误监控** - 捕获JavaScript错误、控制台错误、网络请求失败
- ✅ **MySQL数据库集成** - 连接127.0.0.1:3306，存储测试结果和错误日志
- ✅ **专用webopte.com测试** - 一键快速测试目标网站
- ✅ **可视化界面** - 直观的测试配置、结果查看、历史记录
- ✅ **链接自动发现** - 智能爬取网站所有链接进行测试

## 🚀 快速开始

### 1. 启动服务

```bash
# 方法1: 使用启动脚本（推荐）
./start-website-test.sh

# 方法2: 手动启动
npm run setup-db  # 初始化数据库（首次运行）
npx ts-node backend/simple-website-test-server.ts
```

### 2. 访问测试界面

打开浏览器访问：**http://localhost:3001/**

### 3. 开始测试

- 🎯 **快速测试**: 点击"开始测试 webopte.com"按钮
- 🔧 **自定义测试**: 输入任意网站URL进行测试
- 📈 **查看历史**: 自动显示所有测试历史记录

## 📋 系统要求

- Node.js 16+ 
- MySQL 5.7+ (可选，不配置时使用内存存储)
- 2GB+ 内存

## 🔧 配置说明

### MySQL配置 (可选)

如果要使用MySQL数据库存储，请配置 `.env` 文件：

```env
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=za05&vPx
MYSQL_DATABASE=aitest
```

### 初始化数据库

```bash
# 自动创建数据库和表结构
npm run setup-db

# 测试数据库连接
npm run test-db
```

## 🎯 API接口

### 快速测试 webopte.com
```bash
curl -X POST http://localhost:3001/api/website-test/webopte
```

### 自定义网站测试
```bash
curl -X POST http://localhost:3001/api/website-test/execute \
  -H "Content-Type: application/json" \
  -d '{"websiteUrl": "https://example.com"}'
```

### 获取测试历史
```bash
curl http://localhost:3001/api/website-test/history
```

### 获取测试结果
```bash
curl http://localhost:3001/api/website-test/result/{testId}
```

## 📊 测试报告

每次测试会生成详细报告，包括：

- **基本信息**: 网站URL、测试时间、响应时间
- **链接统计**: 总链接数、成功/失败数量
- **错误分析**: 服务端错误、前端错误分类统计
- **详细结果**: 每个链接的具体测试结果

## 🔍 测试内容

### 1. 服务端错误检测
- HTTP状态码监控 (4xx, 5xx错误)
- 响应时间测量
- 网络连接错误检测

### 2. 前端错误监控
- JavaScript运行时错误
- 控制台错误和警告
- 网络请求失败
- 资源加载错误

### 3. 链接发现与测试
- 自动提取页面所有链接
- 递归爬取内部链接
- 外部链接可访问性检查
- 资源文件完整性验证

## 📁 项目结构

```
├── backend/
│   ├── simple-website-test-server.ts  # 简化版服务器
│   ├── config/database.ts             # MySQL配置
│   └── services/                      # 完整版服务（可扩展）
├── public/
│   └── test-interface.html            # 测试界面
├── scripts/
│   ├── setup-database.sql             # 数据库初始化脚本
│   ├── init-database.js               # 自动初始化脚本
│   └── test-database.js               # 连接测试脚本
├── docs/
│   └── DATABASE_SETUP.md              # 数据库配置指南
└── start-website-test.sh              # 启动脚本
```

## 🛠️ 可用命令

```bash
npm run setup-db      # 初始化数据库
npm run test-db       # 测试数据库连接
npm run dev           # 启动完整开发环境
npm run dev:backend   # 仅启动后端服务
./start-website-test.sh # 启动网站测试服务
```

## 🔧 故障排除

### MySQL连接失败
```bash
# 检查MySQL服务状态
brew services list | grep mysql  # macOS
sudo systemctl status mysql      # Linux

# 启动MySQL服务
brew services start mysql        # macOS
sudo systemctl start mysql       # Linux
```

### 端口被占用
```bash
# 检查3001端口
lsof -i :3001

# 杀死占用进程
kill -9 <PID>
```

### 依赖安装问题
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
```

## 📈 性能优化

- **并发控制**: 限制同时测试的链接数量，避免过载
- **缓存机制**: 重复URL避免重复测试
- **超时设置**: 合理的请求超时时间
- **内存管理**: 大量链接时的内存优化

## 🔒 安全考虑

- **请求限制**: 避免对目标网站造成过大压力
- **用户代理**: 使用标准浏览器User-Agent
- **robots.txt**: 尊重网站的爬虫协议
- **错误处理**: 优雅处理各种异常情况

## 📞 技术支持

如遇问题，请检查：

1. **日志输出**: 查看控制台错误信息
2. **数据库连接**: 运行 `npm run test-db`
3. **网络连接**: 确保能访问目标网站
4. **端口占用**: 确保3001端口可用

## 🎉 开始使用

1. 运行 `./start-website-test.sh`
2. 访问 http://localhost:3001/
3. 点击"开始测试 webopte.com"
4. 查看测试结果和历史记录

**祝您测试愉快！** 🚀
